# PowerShell脚本：批量修复Gradle构建文件中的HTTP仓库地址
# 将所有HTTP协议的Maven仓库地址替换为HTTPS协议

Write-Host "开始修复Gradle构建文件中的HTTP仓库地址..." -ForegroundColor Green

# 获取所有build.gradle文件
$buildFiles = Get-ChildItem -Recurse -Filter "build.gradle"

Write-Host "找到 $($buildFiles.Count) 个build.gradle文件" -ForegroundColor Yellow

foreach ($file in $buildFiles) {
    Write-Host "处理文件: $($file.FullName)" -ForegroundColor Cyan
    
    # 读取文件内容
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # 替换HTTP仓库地址为HTTPS
    $content = $content -replace "http://maven\.aliyun\.com/nexus/content/groups/public/", "https://maven.aliyun.com/repository/public"
    $content = $content -replace "http://repo\.spring\.io/snapshot", "https://repo.spring.io/snapshot"
    $content = $content -replace "http://repo\.spring\.io/milestone", "https://repo.spring.io/milestone"
    
    # 移除不必要的buildscript和allprojects块
    $content = $content -replace "buildscript\s*\{[^}]*repositories\s*\{[^}]*\}[^}]*\}", ""
    $content = $content -replace "allprojects\s*\{[^}]*repositories\s*\{[^}]*\}[^}]*\}", ""
    
    # 清理多余的空行
    $content = $content -replace "\n\s*\n\s*\n", "`n`n"
    
    # 如果内容有变化，则写回文件
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  ✓ 已修复" -ForegroundColor Green
    } else {
        Write-Host "  - 无需修复" -ForegroundColor Gray
    }
}

Write-Host "修复完成！" -ForegroundColor Green
