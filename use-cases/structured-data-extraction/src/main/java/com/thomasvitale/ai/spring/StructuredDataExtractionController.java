package com.thomasvitale.ai.spring;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结构化数据提取控制器
 *
 * 这个控制器演示了如何使用大语言模型从非结构化文本中提取结构化数据。
 * 它专门用于处理医疗文本，从患者记录中提取关键信息。
 *
 * 主要功能：
 * - 从自然语言文本中识别和提取特定字段
 * - 将提取的数据映射到预定义的Java对象结构
 * - 处理缺失信息的情况（不强制填充未知字段）
 *
 * 应用场景：
 * - 医疗记录数字化
 * - 文档信息提取
 * - 数据标准化处理
 *
 * 技术特点：
 * - 使用低温度(0.0)确保输出的一致性和准确性
 * - 直接返回Java对象，无需手动解析JSON
 */
@RestController
class StructuredDataExtractionController {

    /**
     * Spring AI聊天客户端，配置为结构化数据提取模式
     */
    private final ChatClient chatClient;

    /**
     * 构造函数：初始化聊天客户端并配置为确定性输出模式
     *
     * @param chatClientBuilder Spring AI聊天客户端构建器
     */
    StructuredDataExtractionController(ChatClient.Builder chatClientBuilder) {
        this.chatClient = chatClientBuilder.clone()
                .defaultOptions(ChatOptions.builder()
                        .temperature(0.0)  // 设置温度为0，确保输出的确定性和一致性
                        .build())
                .build();
    }

    /**
     * 结构化数据提取端点
     *
     * 从输入的医疗文本中提取患者信息，包括：
     * - 患者姓名
     * - 观察记录（体重、体温、生命体征等）
     * - 诊断信息
     *
     * 提取原则：
     * - 只提取明确存在的信息
     * - 对于不确定或缺失的字段，不进行猜测
     * - 保持数据的准确性和可靠性
     *
     * @param message 包含患者信息的非结构化文本
     * @return 结构化的患者记录对象
     */
    @PostMapping("/extract")
    PatientJournal extract(@RequestBody String message) {
        return chatClient
                .prompt()
                .user(userSpec -> userSpec.text("""
                    Extract structured data from the provided text.
                    If you do not know the value of a field asked to extract,
                    do not include any value for the field in the result.

                    ---------------------
                    TEXT:
                    {text}
                    ---------------------
                    """)
                        .param("text", message))  // 将输入文本作为参数传递
                .call()
                .entity(PatientJournal.class);  // 直接转换为PatientJournal对象
    }

}
