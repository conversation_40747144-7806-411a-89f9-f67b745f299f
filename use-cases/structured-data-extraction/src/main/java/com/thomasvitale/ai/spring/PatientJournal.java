package com.thomasvitale.ai.spring;

import java.util.List;

/**
 * 患者记录数据模型
 *
 * 这个记录类定义了从医疗文本中提取的结构化患者信息的数据结构。
 * 使用Java 14+的record语法，提供了不可变的数据载体。
 *
 * 数据结构包含：
 * - 患者基本信息（姓名）
 * - 观察记录列表（各种医疗观察数据）
 * - 诊断信息
 *
 * 这个模型被Spring AI用于自动将LLM的JSON输出转换为Java对象。
 *
 * @param fullName 患者全名
 * @param observations 观察记录列表，包含各种医疗测量和观察数据
 * @param diagnosis 诊断信息
 */
public record PatientJournal(String fullName, List<Observation> observations, Diagnosis diagnosis) {

    /**
     * 观察记录
     *
     * 表示单个医疗观察或测量结果。
     *
     * @param type 观察类型（体重、体温、生命体征等）
     * @param content 观察内容的详细描述
     */
    public record Observation(Type type, String content) {}

    /**
     * 诊断信息
     *
     * 包含医生的诊断结论或医疗建议。
     *
     * @param content 诊断内容
     */
    public record Diagnosis(String content) {}

    /**
     * 观察类型枚举
     *
     * 定义了支持的医疗观察类型，用于分类不同的医疗数据。
     */
    enum Type {
        /** 体重测量 */
        BODY_WEIGHT,
        /** 体温测量 */
        TEMPERATURE,
        /** 生命体征（血压、心率等） */
        VITAL_SIGNS,
        /** 其他类型的观察 */
        OTHER
    }
}
