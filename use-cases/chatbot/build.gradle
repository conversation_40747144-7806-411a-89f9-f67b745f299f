// 聊天机器人模块构建脚本
// 修复Gradle 9.0+版本的HTTPS仓库要求

plugins {
    // Java插件：提供Java编译和打包功能
    id 'java'
    // Spring Boot插件：提供Spring Boot应用构建功能
    id 'org.springframework.boot'
    // 依赖管理插件：管理Spring Boot依赖版本
    id 'io.spring.dependency-management'
    // GraalVM原生镜像插件：支持编译为原生可执行文件
    id 'org.graalvm.buildtools.native'
}

// 项目基本信息配置
group = 'com.thomasvitale'  // 项目组织名称
version = '0.0.1-SNAPSHOT'  // 项目版本号

// Java工具链配置
java {
    toolchain {
        // 指定使用Java 24版本
        languageVersion = JavaLanguageVersion.of(21)
        // 启用原生镜像支持
        nativeImageCapable = true
    }
}

// Maven仓库配置 - 使用HTTPS协议确保安全性
repositories {
    // Maven中央仓库：官方Java库仓库
    mavenCentral()
    // 阿里云Maven仓库：国内镜像，加速下载（使用HTTPS）
    maven {
        url 'https://maven.aliyun.com/repository/public'
        name 'Aliyun Public Repository'
    }
    // Spring快照仓库：获取Spring AI最新快照版本（使用HTTPS）
    maven {
        url 'https://repo.spring.io/snapshot'
        name 'Spring Snapshot Repository'
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.ai:spring-ai-starter-model-ollama'

    testAndDevelopmentOnly 'io.arconia:arconia-dev-services-ollama'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

dependencyManagement {
    imports {
        mavenBom "io.arconia:arconia-bom:${arconiaVersion}"
        mavenBom "org.springframework.ai:spring-ai-bom:${springAiVersion}"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}
