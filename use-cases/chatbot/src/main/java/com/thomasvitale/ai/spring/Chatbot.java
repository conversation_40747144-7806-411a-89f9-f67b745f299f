package com.thomasvitale.ai.spring;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 聊天机器人应用程序主类
 *
 * 这是一个基于Spring AI的聊天机器人应用示例。
 *
 * 功能特性：
 * - 支持多会话对话管理
 * - 具备对话记忆功能，能够记住之前的对话内容
 * - 使用Ollama作为LLM提供商
 * - 通过REST API提供聊天服务
 *
 * 使用方式：
 * 1. 启动应用程序
 * 2. 向 POST /chat/{conversationId} 发送请求
 * 3. 在请求体中包含用户的问题
 * 4. 系统会返回AI生成的回复
 *
 * 技术栈：
 * - Spring Boot 3.x
 * - Spring AI 1.0
 * - Ollama (本地LLM服务)
 */
@SpringBootApplication
public class Chatbot {

    /**
     * 应用程序入口点
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(Chatbot.class, args);
    }

}
