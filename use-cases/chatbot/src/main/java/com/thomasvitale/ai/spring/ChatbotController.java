// 包声明：定义当前类所属的包路径
package com.thomasvitale.ai.spring;

// 导入Spring AI聊天客户端核心类
import org.springframework.ai.chat.client.ChatClient;
// 导入消息聊天记忆顾问类，用于管理对话历史
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
// 导入聊天记忆接口，定义对话存储的抽象
import org.springframework.ai.chat.memory.ChatMemory;
// 导入Spring Web注解，用于处理路径变量
import org.springframework.web.bind.annotation.PathVariable;
// 导入Spring Web注解，用于处理POST请求
import org.springframework.web.bind.annotation.PostMapping;
// 导入Spring Web注解，用于处理请求体
import org.springframework.web.bind.annotation.RequestBody;
// 导入Spring Web注解，标识REST控制器
import org.springframework.web.bind.annotation.RestController;

// 静态导入对话ID常量，用于在顾问参数中标识会话
import static org.springframework.ai.chat.memory.ChatMemory.CONVERSATION_ID;

/**
 * 聊天机器人控制器
 *
 * 这个控制器实现了一个具有对话记忆功能的聊天机器人。
 * 主要特性：
 * - 支持多个独立的对话会话
 * - 每个会话都有独立的对话历史记忆
 * - 使用Spring AI的ChatClient进行LLM交互
 * - 通过MessageChatMemoryAdvisor管理对话上下文
 */
@RestController // Spring注解：标识这是一个REST控制器，自动处理HTTP请求和响应
class ChatbotController {

    /**
     * Spring AI聊天客户端，用于与大语言模型进行交互
     * 使用final关键字确保引用不可变，提高线程安全性
     */
    private final ChatClient chatClient;

    /**
     * 构造函数：初始化聊天客户端并配置对话记忆功能
     *
     * Spring会自动注入所需的依赖项
     *
     * @param chatClientBuilder Spring AI提供的ChatClient构建器，用于创建配置好的聊天客户端
     * @param chatMemory 对话记忆存储组件，用于保存和检索对话历史
     */
    ChatbotController(ChatClient.Builder chatClientBuilder, ChatMemory chatMemory) {
        // 使用构建器模式创建聊天客户端
        this.chatClient = chatClientBuilder.clone() // 克隆构建器以避免影响其他实例
                .defaultAdvisors( // 配置默认顾问组件
                    MessageChatMemoryAdvisor.builder(chatMemory) // 创建消息聊天记忆顾问
                        .build() // 构建顾问实例
                )
                .build(); // 构建最终的聊天客户端实例
    }

    /**
     * 处理聊天请求的端点
     *
     * 这个方法接收用户的问题，并在指定的对话会话中进行回复。
     * 对话历史会自动保存和加载，确保上下文的连续性。
     *
     * @param conversationId 对话会话ID，用于区分不同的对话
     * @param question 用户提出的问题，从HTTP请求体中获取
     * @return LLM生成的回复内容，作为HTTP响应返回
     */
    @PostMapping("/chat/{conversationId}") // 映射POST请求到指定路径，{conversationId}是路径变量
    String chat(@PathVariable String conversationId, @RequestBody String question) {
        // 使用聊天客户端处理用户请求
        return chatClient
                .prompt() // 开始构建提示
                .user(question)  // 设置用户消息内容
                .advisors(a -> a.param(CONVERSATION_ID, conversationId))  // 配置顾问参数，指定对话会话ID
                .call()  // 调用大语言模型进行推理
                .content();  // 提取并返回生成的文本内容
    }

}
