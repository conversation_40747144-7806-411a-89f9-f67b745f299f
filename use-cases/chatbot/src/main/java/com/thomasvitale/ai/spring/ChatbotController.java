package com.thomasvitale.ai.spring;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.ai.chat.memory.ChatMemory.CONVERSATION_ID;

/**
 * 聊天机器人控制器
 *
 * 这个控制器实现了一个具有对话记忆功能的聊天机器人。
 * 主要特性：
 * - 支持多个独立的对话会话
 * - 每个会话都有独立的对话历史记忆
 * - 使用Spring AI的ChatClient进行LLM交互
 * - 通过MessageChatMemoryAdvisor管理对话上下文
 */
@RestController
class ChatbotController {

    /**
     * Spring AI聊天客户端，用于与大语言模型进行交互
     */
    private final ChatClient chatClient;

    /**
     * 构造函数：初始化聊天客户端并配置对话记忆功能
     *
     * @param chatClientBuilder Spring AI提供的ChatClient构建器
     * @param chatMemory 对话记忆存储组件，用于保存和检索对话历史
     */
    ChatbotController(ChatClient.Builder chatClientBuilder, ChatMemory chatMemory) {
        // 克隆构建器并配置默认的对话记忆顾问
        this.chatClient = chatClientBuilder.clone()
                .defaultAdvisors(MessageChatMemoryAdvisor.builder(chatMemory).build())
                .build();
    }

    /**
     * 处理聊天请求的端点
     *
     * 这个方法接收用户的问题，并在指定的对话会话中进行回复。
     * 对话历史会自动保存和加载，确保上下文的连续性。
     *
     * @param conversationId 对话会话ID，用于区分不同的对话
     * @param question 用户提出的问题
     * @return LLM生成的回复内容
     */
    @PostMapping("/chat/{conversationId}")
    String chat(@PathVariable String conversationId, @RequestBody String question) {
        return chatClient
                .prompt()
                .user(question)  // 设置用户消息
                .advisors(a -> a.param(CONVERSATION_ID, conversationId))  // 指定对话会话ID
                .call()  // 调用LLM
                .content();  // 返回生成的内容
    }

}
