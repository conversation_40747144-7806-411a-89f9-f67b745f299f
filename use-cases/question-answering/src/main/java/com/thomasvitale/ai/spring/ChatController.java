// 包声明：定义当前类所属的包路径
package com.thomasvitale.ai.spring;

// 导入Spring AI聊天客户端核心类
import org.springframework.ai.chat.client.ChatClient;
// 导入检索增强生成顾问类，实现RAG功能
import org.springframework.ai.rag.advisor.RetrievalAugmentationAdvisor;
// 导入向量存储文档检索器，用于从向量数据库检索相关文档
import org.springframework.ai.rag.retrieval.search.VectorStoreDocumentRetriever;
// 导入向量存储接口，定义向量数据库的抽象
import org.springframework.ai.vectorstore.VectorStore;
// 导入Spring Web注解，用于处理POST请求
import org.springframework.web.bind.annotation.PostMapping;
// 导入Spring Web注解，用于处理请求体
import org.springframework.web.bind.annotation.RequestBody;
// 导入Spring Web注解，标识REST控制器
import org.springframework.web.bind.annotation.RestController;

/**
 * 基于文档的问答系统控制器
 *
 * 这个控制器实现了检索增强生成(RAG)模式的问答系统。
 *
 * RAG工作流程：
 * 1. 接收用户问题
 * 2. 在向量数据库中检索相关文档
 * 3. 将检索到的文档作为上下文提供给LLM
 * 4. LLM基于上下文生成准确的答案
 *
 * 主要特性：
 * - 支持基于文档内容的精确问答
 * - 使用向量相似度搜索找到最相关的文档片段
 * - 通过RAG模式提高答案的准确性和可靠性
 */
@RestController // Spring注解：标识这是一个REST控制器，自动处理HTTP请求和响应
class ChatController {

    /**
     * Spring AI聊天客户端，用于与大语言模型进行交互
     * 使用final关键字确保引用不可变，提高线程安全性
     */
    private final ChatClient chatClient;

    /**
     * 检索增强生成顾问，负责文档检索和上下文增强
     * 这是RAG系统的核心组件，自动处理文档检索和上下文注入
     */
    private final RetrievalAugmentationAdvisor retrievalAugmentationAdvisor;

    /**
     * 构造函数：初始化聊天客户端和RAG顾问
     *
     * Spring会自动注入所需的依赖项
     *
     * @param chatClientBuilder Spring AI聊天客户端构建器，用于创建配置好的聊天客户端
     * @param vectorStore 向量存储实例，用于存储和检索文档嵌入向量
     */
    ChatController(ChatClient.Builder chatClientBuilder, VectorStore vectorStore) {
        // 创建聊天客户端实例
        this.chatClient = chatClientBuilder.clone() // 克隆构建器以避免影响其他实例
                .build(); // 构建聊天客户端实例

        // 配置检索增强生成顾问
        this.retrievalAugmentationAdvisor = RetrievalAugmentationAdvisor.builder() // 创建RAG顾问构建器
                .documentRetriever( // 配置文档检索器
                    VectorStoreDocumentRetriever.builder() // 创建向量存储文档检索器构建器
                        .vectorStore(vectorStore)  // 设置向量存储实例
                        .build() // 构建文档检索器
                )
                .build(); // 构建RAG顾问实例
    }

    /**
     * 基于文档的问答端点
     *
     * 这个方法实现了RAG模式的问答：
     * 1. 接收用户问题
     * 2. 自动检索相关文档片段
     * 3. 将文档内容作为上下文提供给LLM
     * 4. 返回基于文档内容的准确答案
     *
     * @param question 用户提出的问题，从HTTP请求体中获取
     * @return 基于文档内容生成的答案，作为HTTP响应返回
     */
    @PostMapping("/chat/doc") // 映射POST请求到指定路径
    String chatWithDocument(@RequestBody String question) {
        // 使用聊天客户端处理基于文档的问答请求
        return chatClient.prompt() // 开始构建提示
                .advisors(retrievalAugmentationAdvisor)  // 使用RAG顾问进行文档检索和上下文增强
                .user(question)  // 设置用户问题
                .call()  // 调用大语言模型进行推理
                .content();  // 提取并返回生成的答案内容
    }

}
