// 包声明：定义当前类所属的包路径
package com.thomasvitale.ai.spring;

// 导入Jakarta注解，用于在Bean初始化后执行方法
import jakarta.annotation.PostConstruct;
// 导入SLF4J日志记录器接口
import org.slf4j.Logger;
// 导入SLF4J日志记录器工厂类
import org.slf4j.LoggerFactory;
// 导入Spring AI文档类，表示一个文档对象
import org.springframework.ai.document.Document;
// 导入Spring AI文本读取器，用于读取文本文件
import org.springframework.ai.reader.TextReader;
// 导入Spring AI令牌文本分割器，用于将长文本分割成小块
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
// 导入Spring AI向量存储接口，定义向量数据库操作
import org.springframework.ai.vectorstore.VectorStore;
// 导入Spring注解，用于注入配置值
import org.springframework.beans.factory.annotation.Value;
// 导入Spring核心资源接口，表示一个资源文件
import org.springframework.core.io.Resource;
// 导入Spring注解，标识这是一个组件
import org.springframework.stereotype.Component;

// 导入Java字符集类，用于指定文件编码
import java.nio.charset.Charset;
// 导入Java ArrayList类，用于创建动态数组
import java.util.ArrayList;
// 导入Java List接口，用于定义列表操作
import java.util.List;

/**
 * 文档摄取管道
 *
 * 这个组件负责将文档加载到向量数据库中，为RAG问答系统提供知识库。
 *
 * 处理流程：
 * 1. 读取文档文件（支持.md和.txt格式）
 * 2. 为文档添加元数据（如位置信息）
 * 3. 将长文档分割成较小的文本块
 * 4. 生成文本嵌入向量
 * 5. 存储到向量数据库中
 *
 * 这个管道在应用启动时自动运行，确保知识库已准备就绪。
 */
@Component // Spring注解：标识这是一个组件，会被Spring容器自动管理
class IngestionPipeline {

    // 创建静态final日志记录器实例，用于记录类的操作日志
    // 使用LoggerFactory.getLogger()方法获取当前类的日志记录器
    private static final Logger logger = LoggerFactory.getLogger(IngestionPipeline.class);

    /**
     * 向量存储实例，用于保存文档的嵌入向量
     * 使用final关键字确保引用不可变，提高线程安全性
     */
    private final VectorStore vectorStore;

    /**
     * 第一个文档文件资源（Markdown格式）
     * 使用@Value注解从classpath中加载指定路径的文件
     * classpath:表示从类路径中查找资源文件
     */
    @Value("classpath:documents/story1.md")
    Resource textFile1;

    /**
     * 第二个文档文件资源（文本格式）
     * 使用@Value注解从classpath中加载指定路径的文件
     */
    @Value("classpath:documents/story2.txt")
    Resource textFile2;

    /**
     * 构造函数：初始化文档摄取管道
     *
     * Spring会自动注入VectorStore依赖
     *
     * @param vectorStore 向量存储实例，用于存储文档嵌入向量
     */
    IngestionPipeline(VectorStore vectorStore) {
        // 将注入的向量存储实例赋值给成员变量
        this.vectorStore = vectorStore;
    }

    /**
     * 文档摄取主流程
     *
     * 在Spring容器初始化完成后自动执行，负责：
     * 1. 加载多个文档文件
     * 2. 为每个文档添加自定义元数据
     * 3. 将文档分割成适合LLM处理的文本块
     * 4. 生成嵌入向量并存储到向量数据库
     */
    @PostConstruct // Jakarta注解：标识此方法在Bean初始化完成后自动执行
    void run() {
        // 创建一个ArrayList来存储所有加载的文档
        // 使用ArrayList因为需要动态添加文档，且访问性能较好
        List<Document> documents = new ArrayList<>();

        // 加载第一个Markdown文档
        logger.info("Loading .md files as Documents"); // 记录日志：开始加载Markdown文件
        var textReader1 = new TextReader(textFile1); // 创建文本读取器实例，传入第一个文件资源
        textReader1.getCustomMetadata().put("location", "North Pole");  // 为文档添加自定义元数据：位置信息
        textReader1.setCharset(Charset.defaultCharset()); // 设置文件字符编码为系统默认编码
        documents.addAll(textReader1.get()); // 读取文档内容并添加到文档列表中

        // 加载第二个文本文档
        logger.info("Loading .txt files as Documents"); // 记录日志：开始加载文本文件
        var textReader2 = new TextReader(textFile2); // 创建文本读取器实例，传入第二个文件资源
        textReader2.getCustomMetadata().put("location", "Italy");  // 为文档添加自定义元数据：位置信息
        textReader2.setCharset(Charset.defaultCharset()); // 设置文件字符编码为系统默认编码
        documents.addAll(textReader2.get()); // 读取文档内容并添加到文档列表中

        // 分割文档并生成嵌入向量存储
        logger.info("Creating and storing Embeddings from Documents"); // 记录日志：开始创建和存储嵌入向量
        // 创建TokenTextSplitter实例，使用默认参数分割文档
        // split()方法将长文档分割成适合LLM上下文窗口的小块
        // vectorStore.add()方法将分割后的文档块转换为嵌入向量并存储到向量数据库
        vectorStore.add(new TokenTextSplitter().split(documents));
    }

}
