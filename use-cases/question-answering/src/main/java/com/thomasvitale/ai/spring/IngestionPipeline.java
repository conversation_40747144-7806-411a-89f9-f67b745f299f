package com.thomasvitale.ai.spring;

import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.TextReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * 文档摄取管道
 *
 * 这个组件负责将文档加载到向量数据库中，为RAG问答系统提供知识库。
 *
 * 处理流程：
 * 1. 读取文档文件（支持.md和.txt格式）
 * 2. 为文档添加元数据（如位置信息）
 * 3. 将长文档分割成较小的文本块
 * 4. 生成文本嵌入向量
 * 5. 存储到向量数据库中
 *
 * 这个管道在应用启动时自动运行，确保知识库已准备就绪。
 */
@Component
class IngestionPipeline {

    private static final Logger logger = LoggerFactory.getLogger(IngestionPipeline.class);

    /**
     * 向量存储，用于保存文档的嵌入向量
     */
    private final VectorStore vectorStore;

    /**
     * 第一个文档文件（Markdown格式）
     */
    @Value("classpath:documents/story1.md")
    Resource textFile1;

    /**
     * 第二个文档文件（文本格式）
     */
    @Value("classpath:documents/story2.txt")
    Resource textFile2;

    /**
     * 构造函数
     *
     * @param vectorStore 向量存储实例
     */
    IngestionPipeline(VectorStore vectorStore) {
        this.vectorStore = vectorStore;
    }

    /**
     * 文档摄取主流程
     *
     * 在Spring容器初始化完成后自动执行，负责：
     * 1. 加载多个文档文件
     * 2. 为每个文档添加自定义元数据
     * 3. 将文档分割成适合LLM处理的文本块
     * 4. 生成嵌入向量并存储到向量数据库
     */
    @PostConstruct
    void run() {
        List<Document> documents = new ArrayList<>();

        // 加载第一个Markdown文档
        logger.info("Loading .md files as Documents");
        var textReader1 = new TextReader(textFile1);
        textReader1.getCustomMetadata().put("location", "North Pole");  // 添加位置元数据
        textReader1.setCharset(Charset.defaultCharset());
        documents.addAll(textReader1.get());

        // 加载第二个文本文档
        logger.info("Loading .txt files as Documents");
        var textReader2 = new TextReader(textFile2);
        textReader2.getCustomMetadata().put("location", "Italy");  // 添加位置元数据
        textReader2.setCharset(Charset.defaultCharset());
        documents.addAll(textReader2.get());

        // 分割文档并生成嵌入向量存储
        logger.info("Creating and storing Embeddings from Documents");
        // 使用TokenTextSplitter将长文档分割成适合LLM上下文窗口的小块
        vectorStore.add(new TokenTextSplitter().split(documents));
    }

}
