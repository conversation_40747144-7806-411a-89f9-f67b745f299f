package com.thomasvitale.ai.spring;

import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 乐器笔记数据摄取管道
 *
 * 这个组件负责为语义搜索系统准备示例数据。
 * 它创建了一系列关于不同乐器音色和情感描述的笔记，
 * 并将它们转换为嵌入向量存储在向量数据库中。
 *
 * 数据特点：
 * - 包含10种不同乐器的描述
 * - 每个描述都包含音色特征和情感联想
 * - 适合演示语义搜索的效果
 *
 * 这些数据可以用来测试语义搜索功能，例如：
 * - 搜索"悲伤的音乐"可能会找到大提琴的描述
 * - 搜索"快乐的声音"可能会找到吉他或木琴的描述
 */
@Component
class IngestionPipeline {

    private static final Logger logger = LoggerFactory.getLogger(IngestionPipeline.class);

    /**
     * 向量存储，用于保存乐器笔记的嵌入向量
     */
    private final VectorStore vectorStore;

    /**
     * 构造函数
     *
     * @param vectorStore 向量存储实例
     */
    IngestionPipeline(VectorStore vectorStore) {
        this.vectorStore = vectorStore;
    }

    /**
     * 数据摄取主流程
     *
     * 在应用启动时自动执行，创建示例乐器笔记数据并存储到向量数据库。
     * 每个笔记都包含对特定乐器音色和情感效果的详细描述。
     */
    @PostConstruct
    void run() {
        // 创建乐器笔记数据集
        var instrumentNotes = List.of(
                new InstrumentNote("The haunting sound of the cello evokes a deep sense of melancholy and introspection."),
                new InstrumentNote("The lively strumming of the acoustic guitar brings forth feelings of joy and carefree summer days."),
                new InstrumentNote("The ethereal notes of the harp create a serene and calming atmosphere, reminiscent of a peaceful dream."),
                new InstrumentNote("The majestic blast of the trumpet instills a sense of triumph and victory."),
                new InstrumentNote("The smooth, mellow tones of the saxophone convey a sense of romance and late-night nostalgia."),
                new InstrumentNote("The rhythmic beats of the drum set evoke a primal energy and raw excitement."),
                new InstrumentNote("The bright, tinkling notes of the xylophone spark a playful and whimsical feeling."),
                new InstrumentNote("The rich harmonies of the piano elicit a profound sense of elegance and emotional depth."),
                new InstrumentNote("The airy sound of the flute carries a feeling of lightness and carefree freedom."),
                new InstrumentNote("The resonant strings of the violin draw out a sense of passionate longing and dramatic intensity.")
        );

        // 将乐器笔记转换为Spring AI文档对象
        logger.info("Creating InstrumentNotes as Documents");
        List<Document> documents = instrumentNotes.stream()
                .map(note -> new Document(note.content()))  // 转换为Document对象
                .toList();

        // 生成嵌入向量并存储到向量数据库
        logger.info("Creating and storing Embeddings from Documents");
        vectorStore.add(documents);  // 自动生成嵌入向量并存储
    }

}
