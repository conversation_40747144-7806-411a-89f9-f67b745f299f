package com.thomasvitale.ai.spring;

import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 语义搜索控制器
 *
 * 这个控制器实现了基于向量相似度的语义搜索功能。
 * 与传统的关键词搜索不同，语义搜索能够理解查询的语义含义，
 * 找到在语义上相关的内容，即使它们没有共同的关键词。
 *
 * 工作原理：
 * 1. 将用户查询转换为嵌入向量
 * 2. 在向量数据库中搜索最相似的文档向量
 * 3. 返回语义上最相关的文档内容
 *
 * 应用场景：
 * - 智能文档检索
 * - 相似内容推荐
 * - 知识库搜索
 */
@RestController
class SemanticSearchController {

    /**
     * 向量存储，用于执行相似度搜索
     */
    private final VectorStore vectorStore;

    /**
     * 构造函数
     *
     * @param vectorStore 向量存储实例，包含预先计算的文档嵌入向量
     */
    SemanticSearchController(VectorStore vectorStore) {
        this.vectorStore = vectorStore;
    }

    /**
     * 语义搜索端点
     *
     * 根据用户查询执行语义搜索，返回最相关的文档内容。
     *
     * 搜索过程：
     * 1. 将查询文本转换为嵌入向量
     * 2. 计算与数据库中所有文档向量的相似度
     * 3. 返回相似度最高的前3个结果
     *
     * @param query 用户的搜索查询
     * @return 按相关性排序的乐器笔记列表（最多3个）
     */
    @PostMapping("/semantic-search")
    List<InstrumentNote> semanticSearch(@RequestBody String query) {
        return vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query(query)  // 搜索查询
                    .topK(3)       // 返回前3个最相关的结果
                    .build())
                .stream()
                .map(document -> new InstrumentNote(document.getText()))  // 转换为业务对象
                .toList();
    }

}
