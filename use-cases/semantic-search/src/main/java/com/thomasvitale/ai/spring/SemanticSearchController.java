// 包声明：定义当前类所属的包路径
package com.thomasvitale.ai.spring;

// 导入Spring AI搜索请求类，用于构建向量搜索查询
import org.springframework.ai.vectorstore.SearchRequest;
// 导入Spring AI向量存储接口，定义向量数据库操作
import org.springframework.ai.vectorstore.VectorStore;
// 导入Spring Web注解，用于处理POST请求
import org.springframework.web.bind.annotation.PostMapping;
// 导入Spring Web注解，用于处理请求体
import org.springframework.web.bind.annotation.RequestBody;
// 导入Spring Web注解，标识REST控制器
import org.springframework.web.bind.annotation.RestController;

// 导入Java List接口，用于定义列表操作
import java.util.List;

/**
 * 语义搜索控制器
 *
 * 这个控制器实现了基于向量相似度的语义搜索功能。
 * 与传统的关键词搜索不同，语义搜索能够理解查询的语义含义，
 * 找到在语义上相关的内容，即使它们没有共同的关键词。
 *
 * 工作原理：
 * 1. 将用户查询转换为嵌入向量
 * 2. 在向量数据库中搜索最相似的文档向量
 * 3. 返回语义上最相关的文档内容
 *
 * 应用场景：
 * - 智能文档检索
 * - 相似内容推荐
 * - 知识库搜索
 */
@RestController // Spring注解：标识这是一个REST控制器，自动处理HTTP请求和响应
class SemanticSearchController {

    /**
     * 向量存储实例，用于执行相似度搜索
     * 使用final关键字确保引用不可变，提高线程安全性
     */
    private final VectorStore vectorStore;

    /**
     * 构造函数：初始化语义搜索控制器
     *
     * Spring会自动注入VectorStore依赖
     *
     * @param vectorStore 向量存储实例，包含预先计算的文档嵌入向量
     */
    SemanticSearchController(VectorStore vectorStore) {
        // 将注入的向量存储实例赋值给成员变量
        this.vectorStore = vectorStore;
    }

    /**
     * 语义搜索端点
     *
     * 根据用户查询执行语义搜索，返回最相关的文档内容。
     *
     * 搜索过程：
     * 1. 将查询文本转换为嵌入向量
     * 2. 计算与数据库中所有文档向量的相似度
     * 3. 返回相似度最高的前3个结果
     *
     * @param query 用户的搜索查询，从HTTP请求体中获取
     * @return 按相关性排序的乐器笔记列表（最多3个），作为HTTP响应返回
     */
    @PostMapping("/semantic-search") // 映射POST请求到指定路径
    List<InstrumentNote> semanticSearch(@RequestBody String query) {
        // 执行向量相似度搜索并处理结果
        return vectorStore.similaritySearch( // 调用向量存储的相似度搜索方法
                SearchRequest.builder() // 创建搜索请求构建器
                    .query(query)  // 设置搜索查询文本
                    .topK(3)       // 设置返回前3个最相关的结果
                    .build()) // 构建搜索请求对象
                .stream() // 将搜索结果转换为流进行处理
                .map(document -> new InstrumentNote(document.getText()))  // 将每个文档转换为InstrumentNote业务对象
                .toList(); // 将流收集为List并返回
    }

}
