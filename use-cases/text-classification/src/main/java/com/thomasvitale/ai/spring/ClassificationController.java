package com.thomasvitale.ai.spring;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 文本分类控制器
 *
 * 这个控制器演示了使用大语言模型进行文本分类的多种方法。
 * 文本分类是NLP中的一个基础任务，用于将文本自动归类到预定义的类别中。
 *
 * 支持的分类方法：
 * 1. 基于类别名称的简单分类
 * 2. 基于类别描述的详细分类
 * 3. 少样本学习（Few-shot Learning）
 * 4. 结构化输出分类
 *
 * 分类类别：
 * - BUSINESS: 商业、金融、市场相关内容
 * - SPORT: 体育、运动、比赛相关内容
 * - TECHNOLOGY: 技术、创新、科技相关内容
 * - OTHER: 其他类别
 *
 * 技术特点：
 * - 使用零温度确保分类结果的一致性
 * - 支持多种提示工程技术
 * - 可返回结构化的分类结果
 */
@RestController
class ClassificationController {

    /**
     * Spring AI聊天客户端，配置为确定性分类模式
     */
    private final ChatClient chatClient;

    /**
     * 构造函数：初始化聊天客户端并配置为确定性输出模式
     *
     * @param chatClientBuilder Spring AI聊天客户端构建器
     */
    ClassificationController(ChatClient.Builder chatClientBuilder) {
        this.chatClient = chatClientBuilder
                .defaultOptions(ChatOptions.builder()
                        .temperature(0.0)  // 设置温度为0，确保分类结果的一致性
                        .build())
                .build();
    }

    /**
     * 基于类别名称的简单文本分类
     *
     * 这是最基础的分类方法，只提供类别名称让LLM进行分类。
     * 适用于类别含义明确、不需要详细解释的场景。
     *
     * @param text 待分类的文本
     * @return 分类结果（BUSINESS, SPORT, TECHNOLOGY, OTHER之一）
     */
    @PostMapping("/classify/class-names")
    String classifyClassNames(@RequestBody String text) {
        return chatClient
                .prompt()
                .system("""
                    Classify the provided text into one of these classes:
                    BUSINESS, SPORT, TECHNOLOGY, OTHER.
                    """)
                .user(text)
                .call()
                .content();
    }

    /**
     * 基于类别描述的详细文本分类
     *
     * 为每个类别提供详细的描述，帮助LLM更准确地理解分类标准。
     * 适用于类别边界模糊或需要精确分类的场景。
     *
     * @param text 待分类的文本
     * @return 分类结果
     */
    @PostMapping("/classify/class-descriptions")
    String classifyClassDescriptions(@RequestBody String text) {
        return chatClient
                .prompt()
                .system("""
                    Classify the provided text into one of these classes.

                    BUSINESS: Commerce, finance, markets, entrepreneurship, corporate developments.
                    SPORT: Athletic events, tournament outcomes, performances of athletes and teams.
                    TECHNOLOGY: innovations and trends in software, artificial intelligence, cybersecurity.
                    OTHER: Anything that doesn't fit into the other categories.
                    """)
                .user(text)
                .call()
                .content();
    }

    /**
     * 基于少样本学习的文本分类（提示版本）
     *
     * 在系统提示中包含示例，展示每个类别的典型文本。
     * 这种方法通过具体例子帮助LLM理解分类模式。
     *
     * 少样本学习的优势：
     * - 提高分类准确性
     * - 减少边界案例的误分类
     * - 为LLM提供具体的参考标准
     *
     * @param text 待分类的文本
     * @return 分类结果
     */
    @PostMapping("/classify/few-shots-prompt")
    String classifyFewShotsPrompt(@RequestBody String text) {
        return chatClient
                .prompt()
                .system("""
                    Classify the provided text into one of these classes.

                    BUSINESS: Commerce, finance, markets, entrepreneurship, corporate developments.
                    SPORT: Athletic events, tournament outcomes, performances of athletes and teams.
                    TECHNOLOGY: innovations and trends in software, artificial intelligence, cybersecurity.
                    OTHER: Anything that doesn't fit into the other categories.

                    ---

                    Text: Clean Energy Startups Make Waves in 2024, Fueling a Sustainable Future.
                    Class: BUSINESS

                    Text: Basketball Phenom Signs Historic Rookie Contract with NBA Team.
                    Class: SPORT

                    Text: Apple Vision Pro and the New UEFA Euro App Deliver an Innovative Entertainment Experience.
                    Class: TECHNOLOGY

                    Text: Culinary Travel, Best Destinations for Food Lovers This Year!
                    Class: OTHER
                    """)
                .user(text)
                .call()
                .content();
    }

    /**
     * 基于少样本学习的文本分类（对话历史版本）
     *
     * 使用对话历史的方式提供示例，每个示例都是一个完整的用户-助手对话。
     * 这种方法更接近真实的对话场景，可能提供更好的上下文理解。
     *
     * 与提示版本的区别：
     * - 使用真实的消息历史而不是文本示例
     * - 更符合LLM的训练模式
     * - 可能获得更好的分类效果
     *
     * @param text 待分类的文本
     * @return 分类结果
     */
    @PostMapping("/classify/few-shots-history")
    String classifyFewShotsHistory(@RequestBody String text) {
        return chatClient
                .prompt()
                .messages(getPromptWithFewShotsHistory())  // 使用预定义的对话历史
                .user(text)
                .call()
                .content();
    }

    /**
     * 结构化输出的文本分类
     *
     * 返回结构化的分类结果而不是纯文本。
     * 这种方法确保输出格式的一致性，便于程序化处理。
     *
     * 优势：
     * - 类型安全的返回值
     * - 避免解析文本输出的错误
     * - 便于集成到其他系统
     *
     * @param text 待分类的文本
     * @return 结构化的分类结果对象
     */
    @PostMapping("/classify/structured-output")
    Classification classifyStructured(@RequestBody String text) {
        return chatClient
                .prompt()
                .messages(getPromptWithFewShotsHistory())
                .user(text)
                .call()
                .entity(Classification.class);  // 直接转换为Java对象
    }

    /**
     * 分类结果记录类
     *
     * 用于封装结构化的分类结果。
     *
     * @param type 分类类型
     */
    record Classification(ClassificationType type) {}

    /**
     * 默认分类端点
     *
     * 提供一个简洁的分类接口，内部使用结构化输出方法。
     *
     * @param text 待分类的文本
     * @return 分类结果
     */
    @PostMapping("/classify")
    Classification classify(@RequestBody String text) {
        return classifyStructured(text);
    }

    /**
     * 构建少样本学习的对话历史
     *
     * 创建一个包含系统消息和多个示例对话的消息列表。
     * 这些示例覆盖了所有分类类别，为LLM提供了丰富的参考。
     *
     * 示例选择原则：
     * - 每个类别至少包含2个示例
     * - 示例文本具有代表性
     * - 覆盖不同的表达方式和内容风格
     *
     * @return 包含系统消息和示例对话的消息列表
     */
    private List<Message> getPromptWithFewShotsHistory() {
        return List.of(
                // 系统消息：定义分类任务和类别描述
                new SystemMessage("""
                    Classify the provided text into one of these classes.

                    BUSINESS: Commerce, finance, markets, entrepreneurship, corporate developments.
                    SPORT: Athletic events, tournament outcomes, performances of athletes and teams.
                    TECHNOLOGY: innovations and trends in software, artificial intelligence, cybersecurity.
                    OTHER: Anything that doesn't fit into the other categories.
                    """),

                // 示例1：技术类
                new UserMessage("Apple Vision Pro and the New UEFA Euro App Deliver an Innovative Entertainment Experience."),
                new AssistantMessage("TECHNOLOGY"),

                // 示例2：商业类
                new UserMessage("Wall Street, Trading Volumes Reach All-Time Highs Amid Market Optimism."),
                new AssistantMessage("BUSINESS"),

                // 示例3：技术类
                new UserMessage("Sony PlayStation 6 Launch, Next-Gen Gaming Experience Redefines Console Performance."),
                new AssistantMessage("TECHNOLOGY"),

                // 示例4：体育类
                new UserMessage("Water Polo Star Secures Landmark Contract with Major League Team."),
                new AssistantMessage("SPORT"),

                // 示例5：其他类
                new UserMessage("Culinary Travel, Best Destinations for Food Lovers This Year!"),
                new AssistantMessage("OTHER"),

                // 示例6：体育类
                new UserMessage("UEFA Euro 2024, Memorable Matches and Record-Breaking Goals Define Tournament Highlights."),
                new AssistantMessage("SPORT"),

                // 示例7：其他类
                new UserMessage("Rock Band Resurgence, Legendary Groups Return to the Stage with Iconic Performances."),
                new AssistantMessage("OTHER")
        );
    }

}
