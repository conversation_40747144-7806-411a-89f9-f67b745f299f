@echo off
echo 更新项目配置为Java 21...

echo 1. 检查Java版本...
java -version

echo.
echo 2. 批量更新build.gradle文件...
powershell -ExecutionPolicy Bypass -File update-to-java21.ps1

echo.
echo 3. 清理Gradle缓存...
gradlew --stop
gradlew clean --no-daemon

echo.
echo 4. 测试构建...
gradlew :use-cases:chatbot:build --no-daemon

if %ERRORLEVEL% EQU 0 (
    echo ✓ 更新成功！项目现在使用Java 21
    echo 可以运行: gradlew :use-cases:chatbot:bootRun
) else (
    echo ✗ 构建失败，请检查配置
)

echo.
echo 请在IntelliJ IDEA中刷新Gradle项目
pause
