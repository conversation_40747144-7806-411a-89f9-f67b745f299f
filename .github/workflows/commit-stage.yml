name: Commit Stage
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

permissions:
  contents: read

jobs:
  build:
    name: Build
    runs-on: ubuntu-24.04
    permissions:
      contents: read
    steps:
      - name: Check out source code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Java
        uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        with:
          java-version: 23
          distribution: temurin
          cache: gradle

      - name: Compile and test
        run: ./gradlew clean assemble --no-daemon
