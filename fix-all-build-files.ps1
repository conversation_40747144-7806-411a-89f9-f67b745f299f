# PowerShell脚本：完整修复所有build.gradle文件
# 解决HTTP仓库、Java版本、buildscript等问题

Write-Host "开始完整修复所有build.gradle文件..." -ForegroundColor Green

# 获取所有build.gradle文件
$buildFiles = Get-ChildItem -Recurse -Filter "build.gradle" -File

Write-Host "找到 $($buildFiles.Count) 个build.gradle文件" -ForegroundColor Yellow

$fixedCount = 0

foreach ($file in $buildFiles) {
    Write-Host "处理文件: $($file.Name) (路径: $($file.DirectoryName))" -ForegroundColor Cyan
    
    try {
        # 读取文件内容
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        $hasChanges = $false
        
        # 1. 移除buildscript块
        if ($content -match "buildscript\s*\{") {
            $content = $content -replace "buildscript\s*\{[^{}]*\{[^{}]*\}[^{}]*\}", ""
            $hasChanges = $true
            Write-Host "  ✓ 移除buildscript块" -ForegroundColor Yellow
        }
        
        # 2. 移除allprojects块
        if ($content -match "allprojects\s*\{") {
            $content = $content -replace "allprojects\s*\{[^{}]*\{[^{}]*\}[^{}]*\}", ""
            $hasChanges = $true
            Write-Host "  ✓ 移除allprojects块" -ForegroundColor Yellow
        }
        
        # 3. 替换HTTP仓库地址为HTTPS
        if ($content -match "http://maven\.aliyun\.com") {
            $content = $content -replace "http://maven\.aliyun\.com/nexus/content/groups/public/", "https://maven.aliyun.com/repository/public"
            $hasChanges = $true
            Write-Host "  ✓ 修复阿里云仓库地址" -ForegroundColor Yellow
        }
        
        if ($content -match "https://maven\.aliyun\.com/nexus/content/groups/public/") {
            $content = $content -replace "https://maven\.aliyun\.com/nexus/content/groups/public/", "https://maven.aliyun.com/repository/public"
            $hasChanges = $true
            Write-Host "  ✓ 更新阿里云仓库地址" -ForegroundColor Yellow
        }
        
        if ($content -match "http://repo\.spring\.io") {
            $content = $content -replace "http://repo\.spring\.io/snapshot", "https://repo.spring.io/snapshot"
            $hasChanges = $true
            Write-Host "  ✓ 修复Spring仓库地址" -ForegroundColor Yellow
        }
        
        # 4. 修改Java版本从24到17
        if ($content -match "languageVersion = JavaLanguageVersion\.of\(24\)") {
            $content = $content -replace "languageVersion = JavaLanguageVersion\.of\(24\)", "languageVersion = JavaLanguageVersion.of(17)"
            $hasChanges = $true
            Write-Host "  ✓ 修改Java版本: 24 → 17" -ForegroundColor Yellow
        }
        
        # 5. 优化仓库配置顺序
        if ($content -match "repositories\s*\{") {
            $repositoriesPattern = "repositories\s*\{[^{}]*\}"
            if ($content -match $repositoriesPattern) {
                $newRepositories = @"
repositories {
    // Maven中央仓库：官方Java库仓库，最稳定
    mavenCentral()
    
    // Spring快照仓库：获取Spring AI最新快照版本
    maven { 
        url 'https://repo.spring.io/snapshot'
        name 'Spring Snapshot Repository'
    }
    
    // 阿里云Maven仓库：国内镜像，加速下载
    maven { 
        url 'https://maven.aliyun.com/repository/public'
        name 'Aliyun Public Repository'
    }
}
"@
                $content = $content -replace $repositoriesPattern, $newRepositories
                $hasChanges = $true
                Write-Host "  ✓ 优化仓库配置" -ForegroundColor Yellow
            }
        }
        
        # 6. 清理多余的空行
        $content = $content -replace "\r?\n\s*\r?\n\s*\r?\n", "`r`n`r`n"
        
        # 如果有变化则保存文件
        if ($hasChanges -or ($content -ne $originalContent)) {
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            Write-Host "  ✓ 文件已修复" -ForegroundColor Green
            $fixedCount++
        } else {
            Write-Host "  - 无需修复" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "  ✗ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n修复完成！" -ForegroundColor Green
Write-Host "总文件数: $($buildFiles.Count)" -ForegroundColor Yellow
Write-Host "已修复: $fixedCount" -ForegroundColor Green

Write-Host "`n接下来的步骤：" -ForegroundColor Cyan
Write-Host "1. 确保Java版本为17+: java -version" -ForegroundColor White
Write-Host "2. 清理缓存: ./gradlew clean --no-daemon" -ForegroundColor White
Write-Host "3. 停止守护进程: ./gradlew --stop" -ForegroundColor White
Write-Host "4. 重新构建: ./gradlew build --no-daemon" -ForegroundColor White
