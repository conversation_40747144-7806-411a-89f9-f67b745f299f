# Spring AI 项目完整构建问题解决方案

## 问题总结

经过分析，Spring AI项目的构建问题主要有以下几个：

1. ✅ **HTTP仓库协议问题** - 已解决
2. ✅ **TLS协议兼容性问题** - 已解决  
3. ❌ **Java版本兼容性问题** - 需要解决

## 当前状态

- **Gradle版本**: 8.14.1 ✅
- **Java版本**: Java 8 (1.8.0_101) ❌ 
- **Spring Boot版本**: 3.4.6 (需要Java 17+) ❌
- **项目配置**: Java 24 (在build.gradle中) ❌

## 核心问题

**Java版本不匹配**:
- 系统JAVA_HOME指向Java 8: `D:\Development\JDK\jdk1.8.0_101`
- Spring Boot 3.4.6需要Java 17+
- 项目配置要求Java 24

## 解决方案

### 方案1: 升级Java版本（推荐）

#### 1.1 下载并安装Java 17+

**推荐选项**:
- **Amazon Corretto 17**: https://aws.amazon.com/corretto/
- **Eclipse Temurin 17**: https://adoptium.net/
- **Oracle JDK 17**: https://www.oracle.com/java/technologies/downloads/

#### 1.2 更新环境变量

```cmd
# 设置新的JAVA_HOME
set JAVA_HOME=D:\Development\JDK\jdk-17

# 更新PATH
set PATH=%JAVA_HOME%\bin;%PATH%
```

#### 1.3 验证安装

```cmd
java -version
# 应该显示Java 17或更高版本

javac -version
# 应该显示相应的编译器版本
```

### 方案2: 使用Gradle工具链（推荐）

如果不想更改系统默认Java版本，可以配置Gradle使用特定的Java版本。

#### 2.1 修改gradle.properties

```properties
# 指定Gradle使用的Java版本
org.gradle.java.home=D:/Development/JDK/jdk-17

# 或者使用Java工具链自动检测
org.gradle.java.installations.auto-detect=true
org.gradle.java.installations.auto-download=true
```

#### 2.2 修改build.gradle中的Java版本

将所有build.gradle文件中的Java版本从24改为17：

```gradle
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)  // 改为17
        nativeImageCapable = true
    }
}
```

### 方案3: 降级Spring Boot版本（不推荐）

如果必须使用Java 8，需要降级Spring Boot版本：

```properties
# gradle.properties
springBootVersion=2.7.18  # 支持Java 8的最后版本
```

**注意**: 这会导致无法使用Spring AI的最新特性。

## 推荐的完整解决步骤

### 步骤1: 安装Java 17

1. **下载Amazon Corretto 17**:
   ```
   https://corretto.aws/downloads/latest/amazon-corretto-17-x64-windows-jdk.msi
   ```

2. **安装到指定目录**:
   ```
   D:\Development\JDK\jdk-17
   ```

3. **更新系统环境变量**:
   - JAVA_HOME = `D:\Development\JDK\jdk-17`
   - PATH = `%JAVA_HOME%\bin;%PATH%`

### 步骤2: 修改项目配置

#### 2.1 更新gradle.properties

```properties
# Gradle构建配置
org.gradle.configuration-cache=false
org.gradle.caching=true
org.gradle.parallel=true

# JVM参数配置 - 使用Java 17并配置TLS协议支持
org.gradle.jvmargs=-Xmx8g -Dhttps.protocols=TLSv1.2,TLSv1.3 -Djdk.tls.client.protocols=TLSv1.2,TLSv1.3

# 指定Java版本
org.gradle.java.home=D:/Development/JDK/jdk-17

# 依赖版本配置
arconiaVersion=0.12.0
jSpecifyVersion=1.0.0
springAiVersion=1.0.0
```

#### 2.2 批量修改Java版本

创建PowerShell脚本修改所有build.gradle文件：

```powershell
# 批量修改Java版本从24到17
Get-ChildItem -Recurse -Filter "build.gradle" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    $content = $content -replace 'languageVersion = JavaLanguageVersion\.of\(24\)', 'languageVersion = JavaLanguageVersion.of(17)'
    Set-Content -Path $_.FullName -Value $content -Encoding UTF8
    Write-Host "Updated Java version in: $($_.Name)"
}
```

### 步骤3: 清理和重新构建

```cmd
# 清理Gradle缓存
./gradlew clean --no-daemon

# 删除Gradle守护进程
./gradlew --stop

# 重新构建
./gradlew build --no-daemon
```

### 步骤4: 验证构建

```cmd
# 测试单个模块
./gradlew :use-cases:chatbot:build --no-daemon

# 测试语义搜索模块
./gradlew :use-cases:semantic-search:build --no-daemon

# 运行应用
./gradlew :use-cases:chatbot:bootRun
```

## 快速修复脚本

创建一个一键修复脚本：

```batch
@echo off
echo 正在修复Spring AI项目构建问题...

echo 1. 修改Java版本配置...
powershell -Command "Get-ChildItem -Recurse -Filter 'build.gradle' | ForEach-Object { $content = Get-Content $_.FullName -Raw; $content = $content -replace 'languageVersion = JavaLanguageVersion\.of\(24\)', 'languageVersion = JavaLanguageVersion.of(17)'; Set-Content -Path $_.FullName -Value $content -Encoding UTF8; Write-Host \"Updated: $($_.Name)\" }"

echo 2. 清理Gradle缓存...
gradlew clean --no-daemon

echo 3. 停止Gradle守护进程...
gradlew --stop

echo 4. 测试构建...
gradlew :use-cases:chatbot:build --no-daemon

echo 修复完成！
pause
```

## 验证清单

构建成功后，验证以下内容：

- [ ] Java版本正确: `java -version` 显示17+
- [ ] Gradle构建成功: `./gradlew build --no-daemon`
- [ ] 应用可以启动: `./gradlew :use-cases:chatbot:bootRun`
- [ ] 没有HTTP仓库错误
- [ ] 没有TLS协议错误
- [ ] 没有Java版本兼容性错误

## 常见问题

### Q: 安装Java 17后仍然使用Java 8
**A**: 检查环境变量JAVA_HOME和PATH是否正确设置，重启命令行窗口。

### Q: Gradle仍然报告Java版本错误
**A**: 运行 `./gradlew --stop` 停止所有Gradle守护进程，然后重新构建。

### Q: 某些模块仍然无法构建
**A**: 检查该模块的build.gradle文件是否还有HTTP仓库地址或Java 24配置。

### Q: 应用启动后无法访问
**A**: 检查端口8080是否被占用，查看应用日志确认启动状态。

## 总结

通过以上步骤，应该能够完全解决Spring AI项目的构建问题：

1. ✅ **HTTP → HTTPS**: 所有仓库地址已更新
2. ✅ **TLS协议**: gradle.properties已配置TLS支持  
3. ✅ **Java版本**: 升级到Java 17并更新项目配置

完成这些修复后，项目应该能够正常构建和运行。
