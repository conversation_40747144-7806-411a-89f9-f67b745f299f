package com.thomasvitale.ai.spring;

import org.springframework.aot.hint.annotation.RegisterReflectionForBinding;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RegisterReflectionForBinding(classes = BookService.Book.class)
public class BookService {

    private static final Map<Integer,Book> books = new ConcurrentHashMap<>();

    static {
        books.put(1, new Book("His Dark Materials", "<PERSON>"));
        books.put(2, new Book("Narnia", "C.S. Lewis"));
        books.put(3, new Book("The Hobbit", "J.R.R. Tolkien"));
        books.put(4, new Book("The Lord of The Rings", "J.R.R. Tolkien"));
        books.put(5, new Book("The Silmarillion", "J.R.R. Tolkien"));
    }

    public List<Book> getBooksByAuthor(Author author) {
        return books.values().stream()
                .filter(book -> author.name().equals(book.author()))
                .toList();
    }

    public List<Author> getAuthorsByBook(List<Book> booksToSearch) {
        return books.values().stream()
                .filter(book -> booksToSearch.stream()
                        .anyMatch(b -> b.title().equals(book.title())))
                .map(book -> new Author(book.author()))
                .toList();
    }

    public record Author(String name) {}
    public record Book(String title, String author) {}

    public record Authors(List<Author> authors) {}
    public record Books(List<Book> books) {}

}
