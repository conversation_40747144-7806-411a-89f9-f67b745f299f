spring:
  ai:
    ollama:
      init:
        pull-model-strategy: when_missing
      chat:
        options:
          model: granite3.2:2b
          num-ctx: 4096
      embedding:
        options:
          model: nomic-embed-text
    vectorstore:
      pgvector:
        initialize-schema: true
        dimensions: 768
        index-type: hnsw
  threads:
    virtual:
      enabled: true

logging:
  level:
    org.springframework.ai.rag: debug

arconia:
  dev:
    services:
      postgresql:
        image-name: pgvector/pgvector:pg17
