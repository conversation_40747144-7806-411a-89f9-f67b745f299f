# Spring AI 模块详细说明文档

## 概述

本文档详细介绍Spring AI项目中每个模块的功能、架构和使用方法。项目采用模块化设计，每个模块专注于特定的AI功能领域。

## 1. Models 模块 - 模型集成

### 1.1 Chat Models - 聊天模型

聊天模型模块提供了与不同LLM提供商的集成实现，支持统一的API接口。

#### 1.1.1 OpenAI 集成 (`models/chat/chat-openai`)

**功能特性**:
- 支持GPT-3.5-turbo、GPT-4等模型
- 流式和非流式响应
- 自定义模型参数配置
- 日志概率输出支持

**核心组件**:
```java
// 高级API控制器
@RestController
class ChatController {
    private final ChatClient chatClient;
    
    // 基础聊天
    @GetMapping("/chat")
    String chat(String question);
    
    // 自定义选项聊天
    @GetMapping("/chat/generic-options")
    String chatGenericOptions(String question);
    
    // 流式聊天
    @GetMapping("/chat/stream")
    Flux<String> chatStream(String question);
}

// 低级API控制器
@RestController
@RequestMapping("/model")
class ChatModelController {
    private final ChatModel chatModel;
    
    // 直接模型调用
    @GetMapping("/chat")
    String chat(String question);
}
```

**配置示例**:
```yaml
spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      chat:
        options:
          model: gpt-3.5-turbo
          temperature: 0.7
```

**使用场景**:
- 生产环境的高质量对话
- 需要最新模型能力的应用
- 对响应质量要求较高的场景

#### 1.1.2 Ollama 集成 (`models/chat/chat-ollama`)

**功能特性**:
- 本地模型部署，数据隐私保护
- 支持多种开源模型(Llama, Mistral, Granite等)
- HuggingFace模型支持
- 自动模型拉取策略

**核心组件**:
```java
@RestController
class ChatController {
    // 基础聊天功能
    @GetMapping("/chat")
    String chat(String question);
    
    // 通用选项配置
    @GetMapping("/chat/generic-options")
    String chatGenericOptions(String question);
    
    // Ollama特定选项
    @GetMapping("/chat/provider-options")
    String chatProviderOptions(String question);
    
    // HuggingFace模型支持
    @GetMapping("/chat/huggingface")
    String chatHuggingFace(String question);
}
```

**配置示例**:
```yaml
spring:
  ai:
    ollama:
      init:
        pull-model-strategy: when_missing
        chat:
          additional-models:
            - hf.co/HuggingFaceTB/SmolLM2-360M-Instruct-GGUF
      chat:
        options:
          model: granite3.2:2b
          temperature: 0.7
```

**使用场景**:
- 本地部署需求
- 数据隐私要求严格的环境
- 开发和测试环境
- 成本敏感的应用

#### 1.1.3 Mistral AI 集成 (`models/chat/chat-mistral-ai`)

**功能特性**:
- 支持Mistral系列模型
- 安全提示功能
- 多语言支持
- 高性能推理

**核心组件**:
```java
@RestController
class ChatController {
    // 基础聊天
    @GetMapping("/chat")
    String chat(String question);
    
    // 提供商特定选项
    @GetMapping("/chat/provider-options")
    String chatProviderOptions(String question);
}
```

**配置示例**:
```yaml
spring:
  ai:
    mistralai:
      api-key: ${MISTRALAI_API_KEY}
      chat:
        options:
          model: open-mistral-7b
          temperature: 0.7
```

#### 1.1.4 多提供商集成 (`models/chat/chat-multiple-providers`)

**功能特性**:
- 同时支持多个LLM提供商
- 统一的API接口
- 动态提供商选择
- 负载均衡和故障转移

**架构设计**:
```java
@RestController
class ChatController {
    private final ChatClient mistralAichatClient;
    private final ChatClient openAichatClient;
    
    // 使用不同提供商的端点
    @GetMapping("/chat/mistral-ai")
    String chatMistralAi(String question);
    
    @GetMapping("/chat/openai")
    String chatOpenAi(String question);
}
```

**使用场景**:
- 多云部署策略
- 提供商故障转移
- 成本优化
- A/B测试

### 1.2 Embedding Models - 嵌入模型

嵌入模型用于将文本转换为向量表示，是RAG系统的核心组件。

#### 1.2.1 OpenAI Embeddings (`models/embedding/embedding-openai`)

**功能特性**:
- text-embedding-ada-002模型
- 高质量向量表示
- 多语言支持
- 批量处理能力

#### 1.2.2 Ollama Embeddings (`models/embedding/embedding-ollama`)

**功能特性**:
- 本地嵌入模型
- nomic-embed-text等开源模型
- 数据隐私保护
- 自定义模型支持

#### 1.2.3 Transformers Embeddings (`models/embedding/embedding-transformers`)

**功能特性**:
- ONNX模型支持
- 离线运行能力
- 高性能推理
- 自定义模型加载

### 1.3 Image Models - 图像模型

#### 1.3.1 OpenAI Image Generation (`models/image/image-openai`)

**功能特性**:
- DALL-E模型支持
- 文本到图像生成
- 图像编辑功能
- 多种尺寸支持

### 1.4 Audio Models - 音频模型

#### 1.4.1 Speech-to-Text (`models/audio/speech-to-text-openai`)

**功能特性**:
- Whisper模型支持
- 多语言语音识别
- 高精度转录
- 实时处理能力

#### 1.4.2 Text-to-Speech (`models/audio/text-to-speech-openai`)

**功能特性**:
- 自然语音合成
- 多种声音选择
- 情感表达支持
- 高质量音频输出

## 2. Patterns 模块 - 设计模式

### 2.1 Prompts - 提示工程

提示工程模块展示了不同的提示技术和最佳实践。

#### 2.1.1 基础提示 (`patterns/prompts/prompts-basics-*`)

**功能特性**:
- 简单文本提示
- 提示对象构建
- 完整响应处理

**核心概念**:
```java
// 简单文本提示
String response = chatClient.prompt("你好").call().content();

// 提示对象
Prompt prompt = new Prompt("你好");
ChatResponse response = chatClient.prompt(prompt).call().chatResponse();

// 完整响应处理
ChatResponse fullResponse = chatClient.prompt("你好").call().chatResponse();
```

#### 2.1.2 消息提示 (`patterns/prompts/prompts-messages-*`)

**功能特性**:
- 结构化消息
- 角色定义(系统、用户、助手)
- 对话历史管理

**消息类型**:
```java
// 系统消息 - 定义AI的行为
SystemMessage systemMessage = new SystemMessage("你是一个有帮助的助手");

// 用户消息 - 用户输入
UserMessage userMessage = new UserMessage("请解释量子计算");

// 助手消息 - AI回复
AssistantMessage assistantMessage = new AssistantMessage("量子计算是...");
```

#### 2.1.3 模板提示 (`patterns/prompts/prompts-templates-*`)

**功能特性**:
- 参数化提示
- 模板变量替换
- 动态内容生成

**模板示例**:
```java
String template = """
    你是一个{role}专家。
    请回答关于{topic}的问题：{question}
    """;

PromptTemplate promptTemplate = new PromptTemplate(template);
Prompt prompt = promptTemplate.create(Map.of(
    "role", "技术",
    "topic", "人工智能",
    "question", "什么是机器学习？"
));
```

### 2.2 Structured Output - 结构化输出

#### 2.2.1 JSON输出 (`patterns/structured-output/structured-output-*`)

**功能特性**:
- 自动JSON解析
- Java对象映射
- 类型安全
- 验证支持

**使用示例**:
```java
// 定义数据结构
record Person(String name, int age, String occupation) {}

// 获取结构化输出
Person person = chatClient
    .prompt("提取这个人的信息：张三，30岁，软件工程师")
    .call()
    .entity(Person.class);
```

### 2.3 Multimodality - 多模态

#### 2.3.1 图像理解 (`patterns/multimodality/multimodality-*`)

**功能特性**:
- 图像内容分析
- 文本+图像输入
- 视觉问答
- 图像描述生成

**使用示例**:
```java
// 图像+文本输入
UserMessage message = new UserMessage(
    "描述这张图片",
    new Media(MimeTypeUtils.IMAGE_JPEG, imageResource)
);
```

### 2.4 Tool Calling - 工具调用

#### 2.4.1 函数调用 (`patterns/tool-calling/tool-calling-*`)

**功能特性**:
- 外部函数集成
- 参数自动解析
- 结果处理
- 工具链组合

**工具定义**:
```java
@Component
public class WeatherService {
    
    @Tool("获取指定城市的天气信息")
    public String getWeather(String city) {
        // 实现天气查询逻辑
        return "北京今天晴天，温度25°C";
    }
}
```

### 2.5 Memory - 记忆管理

#### 2.5.1 基础记忆 (`patterns/memory/memory-basics`)

**功能特性**:
- 内存对话历史
- 会话管理
- 上下文保持

#### 2.5.2 JDBC记忆 (`patterns/memory/memory-jdbc`)

**功能特性**:
- 数据库持久化
- 跨会话记忆
- 查询优化

#### 2.5.3 向量存储记忆 (`patterns/memory/memory-vector-store`)

**功能特性**:
- 语义记忆检索
- 长期记忆管理
- 相关性排序

### 2.6 Guardrails - 安全防护

#### 2.6.1 输入防护 (`patterns/guardrails/guardrails-input`)

**功能特性**:
- 输入内容过滤
- 恶意提示检测
- 内容审核

#### 2.6.2 输出防护 (`patterns/guardrails/guardrails-output`)

**功能特性**:
- 输出内容审核
- 敏感信息过滤
- 合规性检查

## 3. RAG 模块 - 检索增强生成

### 3.1 Sequential RAG - 顺序RAG

#### 3.1.1 简单RAG (`rag/rag-sequential/rag-naive`)

**功能特性**:
- 基础文档检索
- 简单上下文注入
- 快速原型开发

**工作流程**:
1. 文档向量化存储
2. 查询向量化
3. 相似度检索
4. 上下文生成
5. LLM推理

#### 3.1.2 高级RAG (`rag/rag-sequential/rag-advanced`)

**功能特性**:
- 增强检索策略
- 多轮对话支持
- 可观测性集成
- 性能优化

**增强特性**:
- 查询重写
- 文档重排序
- 上下文压缩
- 结果验证

### 3.2 Branching RAG - 分支RAG

#### 3.2.1 并行检索 (`rag/rag-branching`)

**功能特性**:
- 多查询扩展
- 并行文档检索
- 结果聚合
- 性能优化

**核心组件**:
```java
// 多查询扩展器
MultiQueryExpander queryExpander = new MultiQueryExpander();

// 文档连接器
ConcatenationDocumentJoiner documentJoiner = new ConcatenationDocumentJoiner();

// 并行检索配置
RetrievalAugmentationAdvisor advisor = RetrievalAugmentationAdvisor.builder()
    .queryExpander(queryExpander)
    .documentJoiner(documentJoiner)
    .build();
```

### 3.3 Conditional RAG - 条件RAG

#### 3.3.1 智能路由 (`rag/rag-conditional`)

**功能特性**:
- 查询意图识别
- 动态数据源选择
- 条件检索策略
- 智能路由决策

**路由策略**:
```java
// 查询路由器
@Component
public class QueryRouter {
    
    public DataSource routeQuery(String query) {
        if (isFactualQuery(query)) {
            return vectorStore;
        } else if (isRealtimeQuery(query)) {
            return searchEngine;
        }
        return defaultSource;
    }
}
```

## 4. Data Ingestion 模块 - 数据摄取

### 4.1 Document Readers - 文档读取器

#### 4.1.1 文本读取器 (`data-ingestion/document-readers/document-readers-text-ollama`)

**功能特性**:
- 纯文本文件读取
- 编码自动检测
- 元数据提取
- 批量处理

#### 4.1.2 PDF读取器 (`data-ingestion/document-readers/document-readers-pdf-ollama`)

**功能特性**:
- PDF文档解析
- 文本提取
- 表格处理
- 图像识别

#### 4.1.3 Markdown读取器 (`data-ingestion/document-readers/document-readers-markdown-ollama`)

**功能特性**:
- Markdown格式解析
- 结构化内容提取
- 链接处理
- 代码块识别

#### 4.1.4 JSON读取器 (`data-ingestion/document-readers/document-readers-json-ollama`)

**功能特性**:
- JSON数据解析
- 嵌套结构处理
- 字段映射
- 数据验证

#### 4.1.5 Tika读取器 (`data-ingestion/document-readers/document-readers-tika-ollama`)

**功能特性**:
- 多格式文档支持
- Apache Tika集成
- 自动格式检测
- 元数据提取

### 4.2 Document Transformers - 文档转换器

#### 4.2.1 文档分割器 (`data-ingestion/document-transformers/document-transformers-splitters-ollama`)

**功能特性**:
- 智能文本分割
- 上下文保持
- 重叠策略
- 大小控制

**分割策略**:
```java
// 令牌分割器
TokenTextSplitter tokenSplitter = new TokenTextSplitter(500, 50);

// 段落分割器
ParagraphTextSplitter paragraphSplitter = new ParagraphTextSplitter();

// 句子分割器
SentenceTextSplitter sentenceSplitter = new SentenceTextSplitter();
```

#### 4.2.2 元数据增强器 (`data-ingestion/document-transformers/document-transformers-metadata-ollama`)

**功能特性**:
- 关键词提取
- 摘要生成
- 分类标签
- 语言检测

**元数据类型**:
```java
// 文档元数据
Map<String, Object> metadata = Map.of(
    "source", "document.pdf",
    "author", "张三",
    "created_date", "2024-01-15",
    "keywords", List.of("AI", "机器学习", "深度学习"),
    "summary", "这是一篇关于人工智能的文章...",
    "language", "zh-CN",
    "category", "技术文档"
);
```

## 5. Observability 模块 - 可观测性

### 5.1 Models Observability - 模型监控

#### 5.1.1 OpenAI监控 (`observability/models/observability-models-openai`)

**监控指标**:
- 请求响应时间
- 令牌使用量
- 错误率统计
- 成本分析

#### 5.1.2 Ollama监控 (`observability/models/observability-models-ollama`)

**监控指标**:
- 本地模型性能
- 资源使用情况
- 推理延迟
- 并发处理能力

### 5.2 Vector Stores Observability - 向量存储监控

#### 5.2.1 PGVector监控 (`observability/vector-stores/observability-vector-stores-pgvector`)

**监控指标**:
- 查询性能
- 索引效率
- 存储使用量
- 连接池状态

**配置示例**:
```yaml
spring:
  ai:
    vectorstore:
      observations:
        log-query-response: true
        
management:
  endpoints:
    web:
      exposure:
        include: "*"
  metrics:
    export:
      prometheus:
        enabled: true
```

这个详细的模块文档为每个组件提供了完整的功能说明、使用示例和配置指南，帮助开发者深入理解Spring AI项目的架构和实现。
