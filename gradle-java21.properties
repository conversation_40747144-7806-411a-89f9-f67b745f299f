# Gradle build configuration for Java 21
org.gradle.configuration-cache=false
org.gradle.caching=true
org.gradle.parallel=true

# JVM configuration optimized for Java 21
org.gradle.jvmargs=-Xmx8g -Dhttps.protocols=TLSv1.2,TLSv1.3 -Djdk.tls.client.protocols=TLSv1.2,TLSv1.3

# Enable automatic Java detection for Java 21
org.gradle.java.installations.auto-detect=true

# Java 21 specific optimizations
org.gradle.java.installations.auto-download=false

# Dependency versions
arconiaVersion=0.12.0
jSpecifyVersion=1.0.0
springAiVersion=1.0.0
