@echo off
echo 修复Java 8兼容性问题...

echo 1. 替换toolchain配置为简单的兼容性配置...
powershell -Command "Get-ChildItem -Recurse -Filter 'build.gradle' | ForEach-Object { $content = Get-Content $_.FullName -Raw; if ($content -match 'toolchain') { $content = $content -replace 'java\s*\{\s*toolchain\s*\{[^}]*\}[^}]*\}', 'java { sourceCompatibility = JavaVersion.VERSION_1_8; targetCompatibility = JavaVersion.VERSION_1_8 }'; Set-Content $_.FullName $content -Encoding UTF8; Write-Host \"Fixed: $($_.Name)\" } }"

echo 2. 停止Gradle守护进程...
gradlew --stop

echo 3. 清理项目...
gradlew clean --no-daemon

echo 4. 测试构建...
gradlew :use-cases:chatbot:build --no-daemon

echo 修复完成！
pause
