# PowerShell脚本：批量修复所有Gradle构建文件中的HTTP仓库地址
# 将所有HTTP协议的Maven仓库地址替换为HTTPS协议

Write-Host "开始批量修复Gradle构建文件中的HTTP仓库地址..." -ForegroundColor Green

# 获取所有build.gradle文件
$buildFiles = Get-ChildItem -Recurse -Filter "build.gradle" -File

Write-Host "找到 $($buildFiles.Count) 个build.gradle文件" -ForegroundColor Yellow

$fixedCount = 0
$totalCount = $buildFiles.Count

foreach ($file in $buildFiles) {
    Write-Host "处理文件: $($file.Name) (路径: $($file.DirectoryName))" -ForegroundColor Cyan
    
    try {
        # 读取文件内容
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        
        # 检查是否包含HTTP仓库地址
        $hasHttpRepo = $content -match "http://maven\.aliyun\.com" -or $content -match "http://repo\.spring\.io"
        
        if ($hasHttpRepo) {
            # 替换HTTP仓库地址为HTTPS
            $content = $content -replace "http://maven\.aliyun\.com/nexus/content/groups/public/", "https://maven.aliyun.com/repository/public"
            $content = $content -replace "http://repo\.spring\.io/snapshot", "https://repo.spring.io/snapshot"
            $content = $content -replace "http://repo\.spring\.io/milestone", "https://repo.spring.io/milestone"
            
            # 移除不必要的buildscript块（如果存在Android相关内容）
            if ($content -match "buildscript\s*\{[^}]*android[^}]*\}") {
                $content = $content -replace "buildscript\s*\{[^}]*android[^}]*\}", ""
            }
            
            # 移除不必要的allprojects块
            if ($content -match "allprojects\s*\{[^}]*repositories[^}]*\}") {
                $content = $content -replace "allprojects\s*\{[^}]*repositories[^}]*\}", ""
            }
            
            # 清理多余的空行
            $content = $content -replace "\r?\n\s*\r?\n\s*\r?\n", "`r`n`r`n"
            
            # 写回文件
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            Write-Host "  ✓ 已修复HTTP仓库地址" -ForegroundColor Green
            $fixedCount++
        } else {
            Write-Host "  - 无需修复（未发现HTTP仓库）" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "  ✗ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n修复完成！" -ForegroundColor Green
Write-Host "总文件数: $totalCount" -ForegroundColor Yellow
Write-Host "已修复: $fixedCount" -ForegroundColor Green
Write-Host "无需修复: $($totalCount - $fixedCount)" -ForegroundColor Gray

# 验证修复结果
Write-Host "`n正在验证修复结果..." -ForegroundColor Yellow
$remainingHttpFiles = @()

foreach ($file in $buildFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    if ($content -match "http://maven\.aliyun\.com" -or $content -match "http://repo\.spring\.io") {
        $remainingHttpFiles += $file.Name
    }
}

if ($remainingHttpFiles.Count -eq 0) {
    Write-Host "✓ 所有文件已成功修复！" -ForegroundColor Green
} else {
    Write-Host "⚠ 以下文件仍包含HTTP仓库地址，需要手动检查：" -ForegroundColor Yellow
    foreach ($fileName in $remainingHttpFiles) {
        Write-Host "  - $fileName" -ForegroundColor Red
    }
}

Write-Host "`n现在可以尝试运行Gradle构建：" -ForegroundColor Cyan
Write-Host "  ./gradlew :use-cases:chatbot:build --no-daemon" -ForegroundColor White
