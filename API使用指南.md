# Spring AI API 使用指南

## 概述

本文档详细介绍了 Spring AI 项目中各个用例的 API 接口使用方法，包括请求格式、响应示例和常见用法。

## 1. 聊天机器人 API

### 基础信息
- **服务端口**: 8080
- **基础路径**: `/`
- **内容类型**: `application/json`

### 1.1 发送聊天消息

**端点**: `POST /chat/{conversationId}`

**描述**: 在指定的对话会话中发送消息并获取AI回复

**路径参数**:
- `conversationId` (string): 对话会话ID，用于区分不同的对话

**请求体**:
```json
"你好，我想了解Spring AI框架的主要特性"
```

**响应示例**:
```json
"Spring AI是一个强大的框架，主要特性包括：1. 统一的API接口支持多种LLM提供商；2. 内置的RAG支持；3. 对话记忆管理；4. 结构化输出；5. 可观测性和监控功能。它简化了Java应用中AI功能的集成。"
```

**cURL 示例**:
```bash
curl -X POST http://localhost:8080/chat/session-123 \
  -H "Content-Type: application/json" \
  -d "\"什么是检索增强生成(RAG)？\""
```

**特性说明**:
- 支持多轮对话，系统会自动记住对话历史
- 每个 `conversationId` 对应一个独立的对话会话
- 对话历史会持久化存储

## 2. 问答系统 API

### 基础信息
- **服务端口**: 8080
- **特性**: 基于文档的RAG问答

### 2.1 基于文档的问答

**端点**: `POST /chat/doc`

**描述**: 基于预加载的文档内容回答问题

**请求体**:
```json
"文档中提到的主要角色有哪些？"
```

**响应示例**:
```json
"根据文档内容，主要角色包括：1. 北极的探险家约翰，他在寻找失落的宝藏；2. 意大利的艺术家玛丽亚，她正在创作关于自然的画作。这些角色在各自的故事中都面临着不同的挑战和冒险。"
```

**cURL 示例**:
```bash
curl -X POST http://localhost:8080/chat/doc \
  -H "Content-Type: application/json" \
  -d "\"文档中描述的地点有什么特色？\""
```

**工作原理**:
1. 系统自动检索与问题相关的文档片段
2. 将检索到的内容作为上下文提供给LLM
3. LLM基于文档内容生成准确的答案

## 3. 语义搜索 API

### 基础信息
- **服务端口**: 8080
- **特性**: 基于语义理解的智能搜索

### 3.1 语义搜索

**端点**: `POST /semantic-search`

**描述**: 根据语义相似度搜索相关的乐器笔记

**请求体**:
```json
"悲伤的音乐"
```

**响应示例**:
```json
[
  {
    "content": "The haunting sound of the cello evokes a deep sense of melancholy and introspection."
  },
  {
    "content": "The resonant strings of the violin draw out a sense of passionate longing and dramatic intensity."
  },
  {
    "content": "The smooth, mellow tones of the saxophone convey a sense of romance and late-night nostalgia."
  }
]
```

**cURL 示例**:
```bash
curl -X POST http://localhost:8080/semantic-search \
  -H "Content-Type: application/json" \
  -d "\"快乐的声音\""
```

**搜索示例**:

| 查询 | 可能返回的结果 |
|------|----------------|
| "悲伤的音乐" | 大提琴、小提琴的描述 |
| "快乐的声音" | 吉他、木琴的描述 |
| "宁静的氛围" | 竖琴、长笛的描述 |
| "激动人心" | 小号、鼓的描述 |

## 4. 结构化数据提取 API

### 基础信息
- **服务端口**: 8080
- **特性**: 从非结构化文本中提取结构化医疗数据

### 4.1 提取患者信息

**端点**: `POST /extract`

**描述**: 从医疗文本中提取结构化的患者信息

**请求体**:
```json
"患者张三，男，35岁，体重75公斤，体温37.5度，血压130/80mmHg，心率78次/分。患者主诉头痛、发热，持续2天。体格检查：精神状态良好，咽部轻度充血。初步诊断：上呼吸道感染。建议多休息，多饮水，口服退热药物。"
```

**响应示例**:
```json
{
  "fullName": "张三",
  "observations": [
    {
      "type": "BODY_WEIGHT",
      "content": "75公斤"
    },
    {
      "type": "TEMPERATURE", 
      "content": "37.5度"
    },
    {
      "type": "VITAL_SIGNS",
      "content": "血压130/80mmHg，心率78次/分"
    },
    {
      "type": "OTHER",
      "content": "精神状态良好，咽部轻度充血"
    }
  ],
  "diagnosis": {
    "content": "上呼吸道感染。建议多休息，多饮水，口服退热药物。"
  }
}
```

**cURL 示例**:
```bash
curl -X POST http://localhost:8080/extract \
  -H "Content-Type: application/json" \
  -d "\"患者李四，体重68kg，体温36.8℃，血压正常。诊断为健康状态良好。\""
```

**支持的观察类型**:
- `BODY_WEIGHT`: 体重测量
- `TEMPERATURE`: 体温测量  
- `VITAL_SIGNS`: 生命体征(血压、心率等)
- `OTHER`: 其他观察

## 5. 文本分类 API

### 基础信息
- **服务端口**: 8080
- **支持类别**: BUSINESS, SPORT, TECHNOLOGY, OTHER

### 5.1 基于类别名称的分类

**端点**: `POST /classify/class-names`

**描述**: 使用简单的类别名称进行文本分类

**请求体**:
```json
"苹果公司发布了新的iPhone，搭载了先进的AI芯片，性能提升显著。"
```

**响应示例**:
```json
"TECHNOLOGY"
```

### 5.2 基于类别描述的分类

**端点**: `POST /classify/class-descriptions`

**描述**: 使用详细的类别描述进行更准确的分类

**请求体**:
```json
"NBA总决赛第六场比赛中，湖人队以112-108击败凯尔特人队，成功夺得总冠军。"
```

**响应示例**:
```json
"SPORT"
```

### 5.3 少样本学习分类

**端点**: `POST /classify/few-shots-prompt`

**描述**: 使用示例提高分类准确性

**请求体**:
```json
"特斯拉股价今日上涨5%，市值突破8000亿美元，投资者对电动汽车市场前景看好。"
```

**响应示例**:
```json
"BUSINESS"
```

### 5.4 结构化输出分类

**端点**: `POST /classify/structured-output`

**描述**: 返回结构化的分类结果

**请求体**:
```json
"人工智能技术在医疗诊断领域的应用越来越广泛，机器学习算法能够帮助医生更准确地识别疾病。"
```

**响应示例**:
```json
{
  "type": "TECHNOLOGY"
}
```

### 5.5 默认分类接口

**端点**: `POST /classify`

**描述**: 简化的分类接口，使用最佳实践配置

**cURL 示例**:
```bash
curl -X POST http://localhost:8080/classify \
  -H "Content-Type: application/json" \
  -d "\"中国足球队在世界杯预选赛中表现出色，成功晋级下一轮比赛。\""
```

## 6. 错误处理

### 常见错误响应

**400 Bad Request**:
```json
{
  "timestamp": "2024-01-15T10:30:00.000+00:00",
  "status": 400,
  "error": "Bad Request",
  "message": "请求体不能为空",
  "path": "/chat/session-123"
}
```

**500 Internal Server Error**:
```json
{
  "timestamp": "2024-01-15T10:30:00.000+00:00", 
  "status": 500,
  "error": "Internal Server Error",
  "message": "模型服务暂时不可用",
  "path": "/chat/doc"
}
```

## 7. 最佳实践

### 7.1 请求优化

1. **控制输入长度**: 避免发送过长的文本
2. **使用合适的会话ID**: 为不同用户使用不同的会话ID
3. **处理超时**: 设置合理的请求超时时间

### 7.2 错误处理

```javascript
// JavaScript 示例
async function chatWithBot(message, conversationId) {
  try {
    const response = await fetch(`/chat/${conversationId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
      timeout: 30000 // 30秒超时
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('聊天请求失败:', error);
    return '抱歉，服务暂时不可用，请稍后重试。';
  }
}
```

### 7.3 性能优化

1. **批量处理**: 对于多个文档，考虑批量提交
2. **缓存结果**: 对于相同的查询，可以缓存结果
3. **异步处理**: 对于耗时操作，使用异步处理

## 8. 集成示例

### 8.1 Python 客户端

```python
import requests
import json

class SpringAIClient:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
    
    def chat(self, message, conversation_id="default"):
        url = f"{self.base_url}/chat/{conversation_id}"
        response = requests.post(url, json=message)
        return response.json()
    
    def semantic_search(self, query):
        url = f"{self.base_url}/semantic-search"
        response = requests.post(url, json=query)
        return response.json()
    
    def classify_text(self, text):
        url = f"{self.base_url}/classify"
        response = requests.post(url, json=text)
        return response.json()

# 使用示例
client = SpringAIClient()
result = client.chat("你好", "user-123")
print(result)
```

### 8.2 Java 客户端

```java
@Service
public class SpringAIClient {
    
    private final RestTemplate restTemplate;
    private final String baseUrl;
    
    public SpringAIClient(RestTemplate restTemplate, 
                         @Value("${spring-ai.base-url:http://localhost:8080}") String baseUrl) {
        this.restTemplate = restTemplate;
        this.baseUrl = baseUrl;
    }
    
    public String chat(String message, String conversationId) {
        String url = baseUrl + "/chat/" + conversationId;
        return restTemplate.postForObject(url, message, String.class);
    }
    
    public List<InstrumentNote> semanticSearch(String query) {
        String url = baseUrl + "/semantic-search";
        InstrumentNote[] result = restTemplate.postForObject(url, query, InstrumentNote[].class);
        return Arrays.asList(result);
    }
}
```

## 9. 监控和调试

### 9.1 健康检查

**端点**: `GET /actuator/health`

**响应示例**:
```json
{
  "status": "UP",
  "components": {
    "llm": {
      "status": "UP",
      "details": {
        "model": "available"
      }
    },
    "vectorStore": {
      "status": "UP"
    }
  }
}
```

### 9.2 指标监控

**端点**: `GET /actuator/metrics`

可用指标:
- `chat.requests.total`: 聊天请求总数
- `chat.response.time`: 响应时间
- `vector.search.time`: 向量搜索时间

这个API指南为开发者提供了完整的接口使用说明，帮助快速集成和使用Spring AI的各种功能。
