@echo off
echo 批量修复Gradle构建文件中的HTTP仓库地址...

REM 使用PowerShell进行文本替换
powershell -Command "Get-ChildItem -Recurse -Filter 'build.gradle' | ForEach-Object { $content = Get-Content $_.FullName -Raw; $content = $content -replace 'http://maven\.aliyun\.com/nexus/content/groups/public/', 'https://maven.aliyun.com/repository/public'; $content = $content -replace 'http://repo\.spring\.io/snapshot', 'https://repo.spring.io/snapshot'; Set-Content -Path $_.FullName -Value $content -Encoding UTF8; Write-Host \"已修复: $($_.FullName)\" }"

echo 修复完成！
pause
