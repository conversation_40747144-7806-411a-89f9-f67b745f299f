# Spring AI 大语言模型应用开发指南

## 项目概述

这是一个基于 Spring AI 框架的大语言模型(LLM)应用示例集合，展示了如何使用 Java 和 Spring AI 构建各种 AI 驱动的应用程序。项目涵盖了从基础的聊天机器人到复杂的检索增强生成(RAG)系统的完整实现。

### 技术栈

- **Java 24** - 使用最新的 Java 特性
- **Spring Boot 3.4.6** - 现代化的 Spring 框架
- **Spring AI 1.0** - Spring 生态系统的 AI 集成框架
- **Ollama** - 本地大语言模型服务
- **PGVector** - PostgreSQL 向量数据库扩展
- **Gradle** - 构建工具

## 项目结构

```
llm-apps-java-spring-ai/
├── use-cases/                    # 核心用例实现
│   ├── chatbot/                  # 聊天机器人
│   ├── question-answering/       # 问答系统(RAG)
│   ├── semantic-search/          # 语义搜索
│   ├── structured-data-extraction/ # 结构化数据提取
│   └── text-classification/      # 文本分类
├── models/                       # 模型集成示例
│   ├── chat/                     # 聊天模型
│   ├── embedding/                # 嵌入模型
│   ├── image/                    # 图像模型
│   └── audio/                    # 音频模型
├── patterns/                     # 设计模式和技术
│   ├── prompts/                  # 提示工程
│   ├── structured-output/        # 结构化输出
│   ├── multimodality/           # 多模态
│   ├── tool-calling/            # 工具调用
│   ├── memory/                  # 对话记忆
│   └── guardrails/              # 安全防护
├── rag/                         # RAG 实现模式
│   ├── rag-sequential/          # 顺序RAG
│   ├── rag-branching/           # 分支RAG
│   └── rag-conditional/         # 条件RAG
├── data-ingestion/              # 数据摄取
│   ├── document-readers/        # 文档读取器
│   └── document-transformers/   # 文档转换器
└── observability/               # 可观测性
    ├── models/                  # 模型监控
    └── vector-stores/           # 向量存储监控
```

## 核心用例详解

### 1. 聊天机器人 (Chatbot)

**位置**: `use-cases/chatbot/`

**功能特性**:
- 支持多会话对话管理
- 具备对话记忆功能，能够记住之前的对话内容
- 使用 Ollama 作为 LLM 提供商
- 通过 REST API 提供聊天服务

**核心组件**:
- `ChatbotController`: 处理聊天请求的控制器
- `MessageChatMemoryAdvisor`: 管理对话记忆的顾问组件

**API 端点**:
```http
POST /chat/{conversationId}
Content-Type: application/json

"你好，我想了解Spring AI框架"
```

**技术亮点**:
- 使用 `ChatClient` 进行 LLM 交互
- 通过 `conversationId` 区分不同的对话会话
- 自动保存和加载对话历史

### 2. 问答系统 (Question Answering)

**位置**: `use-cases/question-answering/`

**功能特性**:
- 基于文档的精确问答
- 实现检索增强生成(RAG)模式
- 支持多种文档格式(.md, .txt)
- 使用向量相似度搜索

**核心组件**:
- `ChatController`: RAG 问答控制器
- `IngestionPipeline`: 文档摄取管道
- `RetrievalAugmentationAdvisor`: 检索增强顾问

**工作流程**:
1. 文档加载和预处理
2. 文本分割和向量化
3. 存储到向量数据库
4. 用户提问时检索相关文档
5. 基于检索内容生成答案

**API 端点**:
```http
POST /chat/doc
Content-Type: application/json

"文档中提到的主要角色是谁？"
```

### 3. 语义搜索 (Semantic Search)

**位置**: `use-cases/semantic-search/`

**功能特性**:
- 基于语义理解的智能搜索
- 支持模糊匹配和概念搜索
- 返回相关性排序的结果
- 适用于知识库检索

**核心组件**:
- `SemanticSearchController`: 语义搜索控制器
- `IngestionPipeline`: 数据摄取管道
- `InstrumentNote`: 乐器笔记数据模型

**搜索示例**:
```http
POST /semantic-search
Content-Type: application/json

"悲伤的音乐"
```

可能返回关于大提琴的描述，即使查询中没有"大提琴"这个词。

### 4. 结构化数据提取 (Structured Data Extraction)

**位置**: `use-cases/structured-data-extraction/`

**功能特性**:
- 从非结构化文本中提取结构化数据
- 专门处理医疗文本和患者记录
- 自动映射到 Java 对象
- 处理缺失信息的情况

**核心组件**:
- `StructuredDataExtractionController`: 数据提取控制器
- `PatientJournal`: 患者记录数据模型

**提取字段**:
- 患者姓名
- 观察记录(体重、体温、生命体征)
- 诊断信息

**API 端点**:
```http
POST /extract
Content-Type: application/json

"患者张三，体重70公斤，体温37.2度，血压正常。诊断为轻微感冒。"
```

### 5. 文本分类 (Text Classification)

**位置**: `use-cases/text-classification/`

**功能特性**:
- 多种分类方法实现
- 支持少样本学习
- 结构化输出
- 高准确性分类

**分类类别**:
- BUSINESS: 商业、金融、市场
- SPORT: 体育、运动、比赛
- TECHNOLOGY: 技术、创新、科技
- OTHER: 其他类别

**分类方法**:
1. **基于类别名称**: 最简单的分类方法
2. **基于类别描述**: 提供详细的类别说明
3. **少样本学习**: 通过示例提高准确性
4. **结构化输出**: 返回类型安全的结果

**API 端点**:
```http
POST /classify
Content-Type: application/json

"苹果公司发布了新的iPhone，搭载了先进的AI芯片"
```

## RAG 实现模式

### 1. 顺序 RAG (Sequential RAG)

**简单版本** (`rag-naive`):
- 基础的文档检索和生成流程
- 适合简单的问答场景

**高级版本** (`rag-advanced`):
- 增强的检索策略
- 更复杂的文档处理流程
- 支持可观测性和监控

### 2. 分支 RAG (Branching RAG)

**特性**:
- 并行查询扩展
- 多路径检索
- 结果聚合和优化

**核心技术**:
- `MultiQueryExpander`: 查询扩展器
- `ConcatenationDocumentJoiner`: 文档连接器

### 3. 条件 RAG (Conditional RAG)

**特性**:
- 智能查询路由
- 条件性检索策略
- 动态数据源选择

**应用场景**:
- 多数据源查询
- 智能信息路由
- 复杂业务逻辑

## 模型集成

### 支持的模型提供商

1. **OpenAI**
   - GPT-4, GPT-3.5-turbo
   - DALL-E (图像生成)
   - Whisper (语音识别)

2. **Ollama** (本地部署)
   - Llama, Mistral, Granite
   - 本地嵌入模型

3. **Mistral AI**
   - Mistral-7B, Mixtral
   - 多语言支持

### 配置示例

```yaml
spring:
  ai:
    ollama:
      chat:
        options:
          model: granite3.2:2b
          temperature: 0.7
      embedding:
        options:
          model: nomic-embed-text
    vectorstore:
      pgvector:
        initialize-schema: true
        dimensions: 768
```

## 开发指南

### 环境要求

1. **Java 24** - 确保安装了最新版本的 Java
2. **Docker/Podman** - 用于运行数据库和 Ollama
3. **Gradle** - 构建工具

### 快速开始

1. **克隆项目**:
```bash
git clone <repository-url>
cd llm-apps-java-spring-ai
```

2. **启动依赖服务**:
```bash
# 启动 PostgreSQL (带 PGVector)
docker run -d --name postgres-pgvector \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  pgvector/pgvector:pg17

# 启动 Ollama
docker run -d --name ollama \
  -p 11434:11434 \
  ollama/ollama
```

3. **拉取模型**:
```bash
# 拉取聊天模型
docker exec ollama ollama pull granite3.2:2b

# 拉取嵌入模型
docker exec ollama ollama pull nomic-embed-text
```

4. **运行应用**:
```bash
# 运行聊天机器人
./gradlew :use-cases:chatbot:bootRun

# 运行问答系统
./gradlew :use-cases:question-answering:bootRun
```

### 开发最佳实践

1. **提示工程**:
   - 使用清晰、具体的指令
   - 提供示例和上下文
   - 设置适当的温度参数

2. **向量存储优化**:
   - 合理设置文档分块大小
   - 选择合适的嵌入模型
   - 优化检索参数

3. **错误处理**:
   - 实现重试机制
   - 处理模型不可用的情况
   - 提供降级策略

4. **性能优化**:
   - 使用连接池
   - 实现缓存策略
   - 监控资源使用

## 可观测性和监控

### 支持的监控功能

1. **模型监控**:
   - 请求/响应日志
   - 性能指标
   - 错误率统计

2. **向量存储监控**:
   - 查询性能
   - 存储使用情况
   - 检索质量指标

### 配置示例

```yaml
spring:
  ai:
    chat:
      observations:
        log-completion: true
        log-prompt: true
    vectorstore:
      observations:
        log-query-response: true

management:
  endpoints:
    web:
      exposure:
        include: "*"
```

## 扩展和定制

### 添加新的用例

1. 创建新的模块目录
2. 实现控制器和服务类
3. 配置依赖和构建脚本
4. 添加测试用例

### 集成新的模型提供商

1. 添加相应的 Spring AI starter 依赖
2. 配置模型参数
3. 实现自定义配置类
4. 测试集成效果

### 自定义 RAG 流程

1. 实现自定义的 `DocumentRetriever`
2. 创建专用的 `DocumentTransformer`
3. 配置检索策略
4. 优化性能参数

## 常见问题和解决方案

### Q: 如何处理长文档？
A: 使用 `TokenTextSplitter` 将长文档分割成适合 LLM 上下文窗口的小块。

### Q: 如何提高检索准确性？
A: 
- 优化文档分块策略
- 使用更好的嵌入模型
- 调整检索参数
- 添加元数据过滤

### Q: 如何处理多语言内容？
A: 选择支持多语言的嵌入模型，如 `multilingual-e5-large`。

### Q: 如何优化响应速度？
A: 
- 使用本地模型(Ollama)
- 实现结果缓存
- 优化向量检索参数
- 使用异步处理

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 参考资源

- [Spring AI 官方文档](https://docs.spring.io/spring-ai/reference/)
- [Ollama 官方网站](https://ollama.ai/)
- [PGVector 文档](https://github.com/pgvector/pgvector)
