# Spring AI 大语言模型应用开发指南

## 项目概述

这是一个基于 Spring AI 框架的大语言模型(LLM)应用示例集合，展示了如何使用 Java 和 Spring AI 构建各种 AI 驱动的应用程序。项目涵盖了从基础的聊天机器人到复杂的检索增强生成(RAG)系统的完整实现。

### 技术栈

- **Java 24** - 使用最新的 Java 特性
- **Spring Boot 3.4.6** - 现代化的 Spring 框架
- **Spring AI 1.0** - Spring 生态系统的 AI 集成框架
- **Ollama** - 本地大语言模型服务
- **PGVector** - PostgreSQL 向量数据库扩展
- **Gradle** - 构建工具

## 项目结构

```
llm-apps-java-spring-ai/
├── use-cases/                    # 核心用例实现
│   ├── chatbot/                  # 聊天机器人
│   ├── question-answering/       # 问答系统(RAG)
│   ├── semantic-search/          # 语义搜索
│   ├── structured-data-extraction/ # 结构化数据提取
│   └── text-classification/      # 文本分类
├── models/                       # 模型集成示例
│   ├── chat/                     # 聊天模型
│   ├── embedding/                # 嵌入模型
│   ├── image/                    # 图像模型
│   └── audio/                    # 音频模型
├── patterns/                     # 设计模式和技术
│   ├── prompts/                  # 提示工程
│   ├── structured-output/        # 结构化输出
│   ├── multimodality/           # 多模态
│   ├── tool-calling/            # 工具调用
│   ├── memory/                  # 对话记忆
│   └── guardrails/              # 安全防护
├── rag/                         # RAG 实现模式
│   ├── rag-sequential/          # 顺序RAG
│   ├── rag-branching/           # 分支RAG
│   └── rag-conditional/         # 条件RAG
├── data-ingestion/              # 数据摄取
│   ├── document-readers/        # 文档读取器
│   └── document-transformers/   # 文档转换器
└── observability/               # 可观测性
    ├── models/                  # 模型监控
    └── vector-stores/           # 向量存储监控
```

## 核心用例详解

### 1. 聊天机器人 (Chatbot)

**位置**: `use-cases/chatbot/`

**功能特性**:
- 支持多会话对话管理，每个用户可以有独立的对话历史
- 具备对话记忆功能，能够记住之前的对话内容并保持上下文连续性
- 使用 Ollama 作为默认 LLM 提供商，支持本地部署
- 通过 REST API 提供聊天服务，易于集成到各种应用中
- 支持会话持久化，重启后对话历史不丢失

**核心组件**:
- `ChatbotController`: 处理聊天请求的REST控制器
  - 管理多个对话会话
  - 集成对话记忆功能
  - 提供统一的聊天接口
- `MessageChatMemoryAdvisor`: 管理对话记忆的顾问组件
  - 自动保存用户消息和AI回复
  - 在新请求中加载相关的对话历史
  - 支持会话隔离和管理

**技术架构**:
```java
// 聊天客户端配置
ChatClient chatClient = ChatClient.builder(chatModel)
    .defaultAdvisors(MessageChatMemoryAdvisor.builder(chatMemory).build())
    .build();

// 会话管理
@PostMapping("/chat/{conversationId}")
String chat(@PathVariable String conversationId, @RequestBody String question) {
    return chatClient.prompt()
        .user(question)
        .advisors(a -> a.param(CONVERSATION_ID, conversationId))
        .call()
        .content();
}
```

**API 端点**:
```http
POST /chat/{conversationId}
Content-Type: application/json

"你好，我想了解Spring AI框架"
```

**响应示例**:
```json
"你好！Spring AI是一个强大的Java框架，专门用于构建AI驱动的应用程序。它提供了统一的API来集成各种大语言模型，支持OpenAI、Ollama、Mistral AI等多个提供商。主要特性包括聊天功能、RAG检索增强生成、向量存储、对话记忆等。你想了解哪个具体方面呢？"
```

**技术亮点**:
- 使用 `ChatClient` 高级API进行 LLM 交互，简化开发复杂度
- 通过 `conversationId` 区分不同的对话会话，支持多用户并发
- 自动保存和加载对话历史，提供连续的对话体验
- 支持会话隔离，确保不同用户的对话不会相互干扰
- 集成Spring Boot自动配置，开箱即用

**配置文件**:
```yaml
spring:
  ai:
    ollama:
      init:
        pull-model-strategy: when_missing
        embedding:
          include: false
      chat:
        options:
          model: granite3.2:2b
          temperature: 0.7
```

**使用场景**:
- 客服机器人：提供24/7客户支持
- 个人助手：帮助用户处理日常任务
- 教育辅导：提供个性化学习指导
- 技术支持：回答技术问题和故障排除

### 2. 问答系统 (Question Answering)

**位置**: `use-cases/question-answering/`

**功能特性**:
- 基于文档的精确问答，确保答案来源可追溯
- 实现检索增强生成(RAG)模式，结合检索和生成的优势
- 支持多种文档格式(.md, .txt)，可扩展支持PDF、Word等
- 使用向量相似度搜索，理解语义而非仅匹配关键词
- 自动文档预处理和索引，简化部署流程
- 支持元数据过滤，提高检索精度

**核心组件**:
- `ChatController`: RAG 问答控制器
  - 集成检索增强生成功能
  - 处理用户问答请求
  - 管理检索和生成流程
- `IngestionPipeline`: 文档摄取管道
  - 自动加载和处理文档
  - 文档分割和向量化
  - 元数据提取和管理
- `RetrievalAugmentationAdvisor`: 检索增强顾问
  - 查询向量化
  - 相似度检索
  - 上下文构建和注入

**技术架构**:
```java
// RAG顾问配置
RetrievalAugmentationAdvisor ragAdvisor = RetrievalAugmentationAdvisor.builder()
    .documentRetriever(VectorStoreDocumentRetriever.builder()
        .vectorStore(vectorStore)
        .topK(5)  // 检索前5个最相关文档
        .similarityThreshold(0.7)  // 相似度阈值
        .build())
    .build();

// 问答处理
@PostMapping("/chat/doc")
String chatWithDocument(@RequestBody String question) {
    return chatClient.prompt()
        .advisors(ragAdvisor)
        .user(question)
        .call()
        .content();
}
```

**工作流程**:
1. **文档摄取阶段**:
   - 加载文档文件(story1.md, story2.txt)
   - 添加元数据(位置、来源等信息)
   - 使用TokenTextSplitter分割长文档
   - 生成嵌入向量并存储到PGVector数据库

2. **查询处理阶段**:
   - 接收用户问题
   - 将问题转换为嵌入向量
   - 在向量数据库中执行相似度搜索
   - 检索最相关的文档片段

3. **答案生成阶段**:
   - 将检索到的文档作为上下文
   - 构建包含上下文的提示
   - 调用LLM生成基于文档的答案
   - 返回准确、可靠的回复

**数据摄取配置**:
```java
@PostConstruct
void run() {
    List<Document> documents = new ArrayList<>();

    // 加载Markdown文档
    var textReader1 = new TextReader(textFile1);
    textReader1.getCustomMetadata().put("location", "North Pole");
    textReader1.setCharset(Charset.defaultCharset());
    documents.addAll(textReader1.get());

    // 加载文本文档
    var textReader2 = new TextReader(textFile2);
    textReader2.getCustomMetadata().put("location", "Italy");
    textReader2.setCharset(Charset.defaultCharset());
    documents.addAll(textReader2.get());

    // 分割并存储
    vectorStore.add(new TokenTextSplitter().split(documents));
}
```

**API 端点**:
```http
POST /chat/doc
Content-Type: application/json

"文档中提到的主要角色是谁？"
```

**响应示例**:
```json
"根据文档内容，主要角色包括：1. 北极的探险家约翰，他在寻找失落的宝藏，面临着严寒和未知的挑战；2. 意大利的艺术家玛丽亚，她正在创作关于自然的画作，追求艺术的完美表达。这两个角色分别代表了冒险精神和艺术追求。"
```

**配置文件**:
```yaml
spring:
  ai:
    ollama:
      chat:
        options:
          model: granite3.2:2b
          num-ctx: 4096  # 扩大上下文窗口
      embedding:
        options:
          model: nomic-embed-text
    vectorstore:
      pgvector:
        initialize-schema: true
        dimensions: 768
        index-type: hnsw  # 高性能向量索引
```

**使用场景**:
- 企业知识库：基于内部文档回答员工问题
- 技术文档助手：帮助开发者快速找到API使用方法
- 法律咨询：基于法律条文提供准确的法律建议
- 医疗信息查询：基于医学文献回答健康问题
- 学术研究：基于论文数据库回答研究问题

### 3. 语义搜索 (Semantic Search)

**位置**: `use-cases/semantic-search/`

**功能特性**:
- 基于语义理解的智能搜索，理解查询的深层含义
- 支持模糊匹配和概念搜索，不依赖精确关键词匹配
- 返回相关性排序的结果，按语义相似度排序
- 适用于知识库检索、内容推荐、相似文档查找
- 支持多语言语义理解
- 实时搜索响应，毫秒级查询速度

**核心组件**:
- `SemanticSearchController`: 语义搜索控制器
  - 处理搜索请求
  - 执行向量相似度计算
  - 返回排序后的搜索结果
- `IngestionPipeline`: 数据摄取管道
  - 预处理示例数据
  - 生成文档嵌入向量
  - 存储到向量数据库
- `InstrumentNote`: 乐器笔记数据模型
  - 封装乐器描述信息
  - 提供业务对象抽象

**技术架构**:
```java
// 语义搜索实现
@PostMapping("/semantic-search")
List<InstrumentNote> semanticSearch(@RequestBody String query) {
    return vectorStore.similaritySearch(
        SearchRequest.builder()
            .query(query)
            .topK(3)  // 返回前3个最相关结果
            .similarityThreshold(0.5)  // 设置相似度阈值
            .build())
        .stream()
        .map(document -> new InstrumentNote(document.getText()))
        .toList();
}
```

**示例数据集**:
项目包含10个精心设计的乐器描述，涵盖不同的音色特征和情感表达：

```java
var instrumentNotes = List.of(
    new InstrumentNote("The haunting sound of the cello evokes a deep sense of melancholy and introspection."),
    new InstrumentNote("The lively strumming of the acoustic guitar brings forth feelings of joy and carefree summer days."),
    new InstrumentNote("The ethereal notes of the harp create a serene and calming atmosphere, reminiscent of a peaceful dream."),
    new InstrumentNote("The majestic blast of the trumpet instills a sense of triumph and victory."),
    new InstrumentNote("The smooth, mellow tones of the saxophone convey a sense of romance and late-night nostalgia."),
    // ... 更多乐器描述
);
```

**搜索示例和结果**:

1. **情感搜索**:
```http
POST /semantic-search
Content-Type: application/json

"悲伤的音乐"
```

**返回结果**:
```json
[
  {
    "content": "The haunting sound of the cello evokes a deep sense of melancholy and introspection."
  },
  {
    "content": "The resonant strings of the violin draw out a sense of passionate longing and dramatic intensity."
  },
  {
    "content": "The smooth, mellow tones of the saxophone convey a sense of romance and late-night nostalgia."
  }
]
```

2. **氛围搜索**:
```http
POST /semantic-search
Content-Type: application/json

"宁静的环境"
```

**返回结果**:
```json
[
  {
    "content": "The ethereal notes of the harp create a serene and calming atmosphere, reminiscent of a peaceful dream."
  },
  {
    "content": "The airy sound of the flute carries a feeling of lightness and carefree freedom."
  }
]
```

3. **能量搜索**:
```http
POST /semantic-search
Content-Type: application/json

"充满活力"
```

**返回结果**:
```json
[
  {
    "content": "The rhythmic beats of the drum set evoke a primal energy and raw excitement."
  },
  {
    "content": "The lively strumming of the acoustic guitar brings forth feelings of joy and carefree summer days."
  },
  {
    "content": "The majestic blast of the trumpet instills a sense of triumph and victory."
  }
]
```

**语义搜索优势**:
- **概念理解**: 能理解"悲伤"与"melancholy"的语义关联
- **跨语言搜索**: 中文查询可以匹配英文内容
- **情感匹配**: 基于情感色彩进行内容匹配
- **上下文感知**: 理解查询的整体语境而非单个词汇

**配置文件**:
```yaml
spring:
  ai:
    ollama:
      embedding:
        options:
          model: nomic-embed-text  # 高质量嵌入模型
    vectorstore:
      pgvector:
        initialize-schema: true
        dimensions: 768
        index-type: hnsw
        distance-type: cosine  # 余弦相似度
```

**使用场景**:
- **内容推荐系统**: 基于用户兴趣推荐相关内容
- **智能客服**: 理解用户问题意图，匹配最佳答案
- **文档检索**: 在大型文档库中快速找到相关信息
- **产品搜索**: 电商平台的智能商品搜索
- **学术研究**: 在论文数据库中查找相关研究
- **新闻聚合**: 根据主题聚合相关新闻报道

### 4. 结构化数据提取 (Structured Data Extraction)

**位置**: `use-cases/structured-data-extraction/`

**功能特性**:
- 从非结构化文本中提取结构化数据
- 专门处理医疗文本和患者记录
- 自动映射到 Java 对象
- 处理缺失信息的情况

**核心组件**:
- `StructuredDataExtractionController`: 数据提取控制器
- `PatientJournal`: 患者记录数据模型

**提取字段**:
- 患者姓名
- 观察记录(体重、体温、生命体征)
- 诊断信息

**API 端点**:
```http
POST /extract
Content-Type: application/json

"患者张三，体重70公斤，体温37.2度，血压正常。诊断为轻微感冒。"
```

### 5. 文本分类 (Text Classification)

**位置**: `use-cases/text-classification/`

**功能特性**:
- 多种分类方法实现
- 支持少样本学习
- 结构化输出
- 高准确性分类

**分类类别**:
- BUSINESS: 商业、金融、市场
- SPORT: 体育、运动、比赛
- TECHNOLOGY: 技术、创新、科技
- OTHER: 其他类别

**分类方法**:
1. **基于类别名称**: 最简单的分类方法
2. **基于类别描述**: 提供详细的类别说明
3. **少样本学习**: 通过示例提高准确性
4. **结构化输出**: 返回类型安全的结果

**API 端点**:
```http
POST /classify
Content-Type: application/json

"苹果公司发布了新的iPhone，搭载了先进的AI芯片"
```

## RAG 实现模式

### 1. 顺序 RAG (Sequential RAG)

**简单版本** (`rag-naive`):
- 基础的文档检索和生成流程
- 适合简单的问答场景

**高级版本** (`rag-advanced`):
- 增强的检索策略
- 更复杂的文档处理流程
- 支持可观测性和监控

### 2. 分支 RAG (Branching RAG)

**特性**:
- 并行查询扩展
- 多路径检索
- 结果聚合和优化

**核心技术**:
- `MultiQueryExpander`: 查询扩展器
- `ConcatenationDocumentJoiner`: 文档连接器

### 3. 条件 RAG (Conditional RAG)

**特性**:
- 智能查询路由
- 条件性检索策略
- 动态数据源选择

**应用场景**:
- 多数据源查询
- 智能信息路由
- 复杂业务逻辑

## 模型集成

### 支持的模型提供商

1. **OpenAI**
   - GPT-4, GPT-3.5-turbo
   - DALL-E (图像生成)
   - Whisper (语音识别)

2. **Ollama** (本地部署)
   - Llama, Mistral, Granite
   - 本地嵌入模型

3. **Mistral AI**
   - Mistral-7B, Mixtral
   - 多语言支持

### 配置示例

```yaml
spring:
  ai:
    ollama:
      chat:
        options:
          model: granite3.2:2b
          temperature: 0.7
      embedding:
        options:
          model: nomic-embed-text
    vectorstore:
      pgvector:
        initialize-schema: true
        dimensions: 768
```

## 开发指南

### 环境要求

1. **Java 24** - 确保安装了最新版本的 Java
2. **Docker/Podman** - 用于运行数据库和 Ollama
3. **Gradle** - 构建工具

### 快速开始

1. **克隆项目**:
```bash
git clone <repository-url>
cd llm-apps-java-spring-ai
```

2. **启动依赖服务**:
```bash
# 启动 PostgreSQL (带 PGVector)
docker run -d --name postgres-pgvector \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  pgvector/pgvector:pg17

# 启动 Ollama
docker run -d --name ollama \
  -p 11434:11434 \
  ollama/ollama
```

3. **拉取模型**:
```bash
# 拉取聊天模型
docker exec ollama ollama pull granite3.2:2b

# 拉取嵌入模型
docker exec ollama ollama pull nomic-embed-text
```

4. **运行应用**:
```bash
# 运行聊天机器人
./gradlew :use-cases:chatbot:bootRun

# 运行问答系统
./gradlew :use-cases:question-answering:bootRun
```

### 开发最佳实践

1. **提示工程**:
   - 使用清晰、具体的指令
   - 提供示例和上下文
   - 设置适当的温度参数

2. **向量存储优化**:
   - 合理设置文档分块大小
   - 选择合适的嵌入模型
   - 优化检索参数

3. **错误处理**:
   - 实现重试机制
   - 处理模型不可用的情况
   - 提供降级策略

4. **性能优化**:
   - 使用连接池
   - 实现缓存策略
   - 监控资源使用

## 可观测性和监控

### 支持的监控功能

1. **模型监控**:
   - 请求/响应日志
   - 性能指标
   - 错误率统计

2. **向量存储监控**:
   - 查询性能
   - 存储使用情况
   - 检索质量指标

### 配置示例

```yaml
spring:
  ai:
    chat:
      observations:
        log-completion: true
        log-prompt: true
    vectorstore:
      observations:
        log-query-response: true

management:
  endpoints:
    web:
      exposure:
        include: "*"
```

## 扩展和定制

### 添加新的用例

1. 创建新的模块目录
2. 实现控制器和服务类
3. 配置依赖和构建脚本
4. 添加测试用例

### 集成新的模型提供商

1. 添加相应的 Spring AI starter 依赖
2. 配置模型参数
3. 实现自定义配置类
4. 测试集成效果

### 自定义 RAG 流程

1. 实现自定义的 `DocumentRetriever`
2. 创建专用的 `DocumentTransformer`
3. 配置检索策略
4. 优化性能参数

## 常见问题和解决方案

### Q: 如何处理长文档？
A: 使用 `TokenTextSplitter` 将长文档分割成适合 LLM 上下文窗口的小块。

### Q: 如何提高检索准确性？
A: 
- 优化文档分块策略
- 使用更好的嵌入模型
- 调整检索参数
- 添加元数据过滤

### Q: 如何处理多语言内容？
A: 选择支持多语言的嵌入模型，如 `multilingual-e5-large`。

### Q: 如何优化响应速度？
A: 
- 使用本地模型(Ollama)
- 实现结果缓存
- 优化向量检索参数
- 使用异步处理

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 参考资源

- [Spring AI 官方文档](https://docs.spring.io/spring-ai/reference/)
- [Ollama 官方网站](https://ollama.ai/)
- [PGVector 文档](https://github.com/pgvector/pgvector)
