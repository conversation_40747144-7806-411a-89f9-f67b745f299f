// 包声明：定义当前类所属的包路径
package com.thomasvitale.ai.spring;

// 导入Spring AI聊天客户端核心类
import org.springframework.ai.chat.client.ChatClient;
// 导入Mistral AI聊天模型实现类
import org.springframework.ai.mistralai.MistralAiChatModel;
// 导入Mistral AI聊天选项配置类
import org.springframework.ai.mistralai.MistralAiChatOptions;
// 导入Mistral AI API接口，包含模型常量定义
import org.springframework.ai.mistralai.api.MistralAiApi;
// 导入OpenAI聊天模型实现类
import org.springframework.ai.openai.OpenAiChatModel;
// 导入OpenAI聊天选项配置类
import org.springframework.ai.openai.OpenAiChatOptions;
// 导入OpenAI API接口，包含模型常量定义
import org.springframework.ai.openai.api.OpenAiApi;
// 导入Spring Web注解，用于处理GET请求
import org.springframework.web.bind.annotation.GetMapping;
// 导入Spring Web注解，标识REST控制器
import org.springframework.web.bind.annotation.RestController;

/**
 * 多提供商聊天控制器
 *
 * 这个控制器演示了如何在同一个应用中集成多个LLM提供商。
 * 支持的提供商：
 * - Mistral AI: 开源和商业模型
 * - OpenAI: GPT系列模型
 *
 * 使用高级ChatClient API进行交互，提供统一的编程接口。
 */
@RestController // Spring注解：标识这是一个REST控制器，自动处理HTTP请求和响应
class ChatController {

    /**
     * Mistral AI聊天客户端实例
     * 使用final关键字确保引用不可变，提高线程安全性
     */
    private final ChatClient mistralAichatClient;

    /**
     * OpenAI聊天客户端实例
     * 使用final关键字确保引用不可变，提高线程安全性
     */
    private final ChatClient openAichatClient;

    /**
     * 构造函数：初始化多个聊天客户端
     *
     * Spring会自动注入不同提供商的聊天模型实例
     *
     * @param mistralAiChatModel Mistral AI聊天模型实例，由Spring自动配置
     * @param openAiChatModel OpenAI聊天模型实例，由Spring自动配置
     */
    ChatController(MistralAiChatModel mistralAiChatModel, OpenAiChatModel openAiChatModel) {
        // 为Mistral AI模型创建聊天客户端
        this.mistralAichatClient = ChatClient.builder(mistralAiChatModel) // 使用Mistral AI模型创建构建器
                .build(); // 构建聊天客户端实例

        // 为OpenAI模型创建聊天客户端
        this.openAichatClient = ChatClient.builder(openAiChatModel) // 使用OpenAI模型创建构建器
                .build(); // 构建聊天客户端实例
    }

    /**
     * 使用Mistral AI进行聊天的端点
     *
     * @param question 用户提出的问题，作为查询参数传入
     * @return Mistral AI模型生成的回复内容
     */
    @GetMapping("/chat/mistral-ai") // 映射GET请求到指定路径
    String chatMistralAi(String question) {
        // 使用Mistral AI聊天客户端处理用户请求
        return mistralAichatClient
                .prompt(question) // 设置用户提示
                .call() // 调用Mistral AI模型
                .content(); // 提取并返回生成的文本内容
    }

    /**
     * 使用OpenAI进行聊天的端点
     *
     * @param question 用户提出的问题，作为查询参数传入
     * @return OpenAI模型生成的回复内容
     */
    @GetMapping("/chat/openai") // 映射GET请求到指定路径
    String chatOpenAi(String question) {
        // 使用OpenAI聊天客户端处理用户请求
        return openAichatClient
                .prompt(question) // 设置用户提示
                .call() // 调用OpenAI模型
                .content(); // 提取并返回生成的文本内容
    }

    @GetMapping("/chat/mistral-ai-options")
    String chatMistralAiOptions(String question) {
        return mistralAichatClient
                .prompt(question)
                .options(MistralAiChatOptions.builder()
                        .model(MistralAiApi.ChatModel.SMALL.getValue())
                        .temperature(1.0)
                        .build())
                .call()
                .content();
    }

    @GetMapping("/chat/openai-options")
    String chatOpenAiOptions(String question) {
        return openAichatClient
                .prompt(question)
                .options(OpenAiChatOptions.builder()
                        .model(OpenAiApi.ChatModel.GPT_4_O_MINI.getValue())
                        .temperature(1.0)
                        .build())
                .call()
                .content();
    }

}
