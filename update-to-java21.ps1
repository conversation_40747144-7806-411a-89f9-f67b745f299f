# PowerShell脚本：批量更新所有build.gradle文件为Java 21配置

Write-Host "开始更新所有build.gradle文件为Java 21配置..." -ForegroundColor Green

# 获取所有build.gradle文件
$buildFiles = Get-ChildItem -Recurse -Filter "build.gradle" -File

Write-Host "找到 $($buildFiles.Count) 个build.gradle文件" -ForegroundColor Yellow

$fixedCount = 0

foreach ($file in $buildFiles) {
    Write-Host "处理文件: $($file.Name) (路径: $($file.DirectoryName))" -ForegroundColor Cyan
    
    try {
        # 读取文件内容
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        $hasChanges = $false
        
        # 1. 替换Java 8配置为Java 21
        if ($content -match "sourceCompatibility = JavaVersion\.VERSION_1_8") {
            $content = $content -replace "sourceCompatibility = JavaVersion\.VERSION_1_8", "sourceCompatibility = JavaVersion.VERSION_21"
            $content = $content -replace "targetCompatibility = JavaVersion\.VERSION_1_8", "targetCompatibility = JavaVersion.VERSION_21"
            $hasChanges = $true
            Write-Host "  ✓ 更新Java 8配置为Java 21" -ForegroundColor Yellow
        }
        
        # 2. 替换Java 17配置为Java 21
        if ($content -match "languageVersion = JavaLanguageVersion\.of\(17\)") {
            $content = $content -replace "languageVersion = JavaLanguageVersion\.of\(17\)", "languageVersion = JavaLanguageVersion.of(21)"
            $hasChanges = $true
            Write-Host "  ✓ 更新Java 17配置为Java 21" -ForegroundColor Yellow
        }
        
        # 3. 替换Java 24配置为Java 21
        if ($content -match "languageVersion = JavaLanguageVersion\.of\(24\)") {
            $content = $content -replace "languageVersion = JavaLanguageVersion\.of\(24\)", "languageVersion = JavaLanguageVersion.of(21)"
            $hasChanges = $true
            Write-Host "  ✓ 更新Java 24配置为Java 21" -ForegroundColor Yellow
        }
        
        # 4. 如果使用简单的兼容性配置，替换为工具链配置
        if ($content -match "sourceCompatibility = JavaVersion\.VERSION_21" -and $content -notmatch "toolchain") {
            $simpleJavaPattern = "java\s*\{\s*sourceCompatibility = JavaVersion\.VERSION_21\s*targetCompatibility = JavaVersion\.VERSION_21\s*\}"
            $newToolchainConfig = @"
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
        nativeImageCapable = true
    }
}
"@
            $content = $content -replace $simpleJavaPattern, $newToolchainConfig
            $hasChanges = $true
            Write-Host "  ✓ 升级为工具链配置" -ForegroundColor Yellow
        }
        
        # 如果有变化则保存文件
        if ($hasChanges) {
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            $fixedCount++
            Write-Host "  ✓ 文件已更新" -ForegroundColor Green
        } else {
            Write-Host "  - 无需更新" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "  ✗ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n更新完成！" -ForegroundColor Green
Write-Host "总文件数: $($buildFiles.Count)" -ForegroundColor Yellow
Write-Host "已更新: $fixedCount" -ForegroundColor Green

# 验证Java版本
Write-Host "`n验证Java版本..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1 | Select-String "version" | Select-Object -First 1
    Write-Host "当前Java版本: $javaVersion" -ForegroundColor Cyan
    
    if ($javaVersion -match "21\.") {
        Write-Host "✓ Java 21已正确安装" -ForegroundColor Green
    } else {
        Write-Host "⚠ 警告: 系统Java版本不是21，请检查JAVA_HOME设置" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ 无法检测Java版本" -ForegroundColor Red
}

Write-Host "`n接下来的步骤：" -ForegroundColor Cyan
Write-Host "1. 确保JAVA_HOME指向Java 21安装目录" -ForegroundColor White
Write-Host "2. 清理缓存: ./gradlew clean --no-daemon" -ForegroundColor White
Write-Host "3. 重新构建: ./gradlew build --no-daemon" -ForegroundColor White
Write-Host "4. 在IntelliJ IDEA中刷新Gradle项目" -ForegroundColor White
