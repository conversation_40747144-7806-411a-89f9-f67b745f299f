# Java 8 环境下的 Spring AI 项目解决方案

## 问题分析

当前环境状况：
- **系统Java版本**: Java 8 (1.8.0_101)
- **Spring Boot版本**: 3.4.6 (需要Java 17+)
- **Gradle版本**: 8.14.1
- **项目配置**: Java 17/24

## 核心冲突

Spring Boot 3.x 系列**不支持Java 8**，最低要求是Java 17。这是一个硬性要求，无法绕过。

## 解决方案

### 方案1: 升级Java版本（强烈推荐）

这是唯一能完全解决问题的方案。

#### 1.1 下载并安装Java 17

**推荐下载**:
- **Amazon Corretto 17**: https://corretto.aws/downloads/latest/amazon-corretto-17-x64-windows-jdk.msi
- **Eclipse Temurin 17**: https://adoptium.net/temurin/releases/?version=17
- **Oracle JDK 17**: https://www.oracle.com/java/technologies/downloads/#java17

#### 1.2 安装步骤

1. **下载安装包**并运行
2. **选择安装目录**（建议：`D:\Development\JDK\jdk-17`）
3. **更新环境变量**：
   ```
   JAVA_HOME = D:\Development\JDK\jdk-17
   PATH = %JAVA_HOME%\bin;%PATH%
   ```
4. **验证安装**：
   ```cmd
   java -version
   # 应该显示Java 17.x.x
   ```

#### 1.3 修复项目配置

1. **替换gradle.properties**：
   ```cmd
   copy gradle.properties.new gradle.properties
   ```

2. **清理并重建**：
   ```cmd
   ./gradlew clean --no-daemon
   ./gradlew build --no-daemon
   ```

### 方案2: 降级Spring Boot版本（不推荐）

如果必须使用Java 8，需要大幅降级项目版本。

#### 2.1 修改gradle.properties

```properties
# 降级到支持Java 8的版本
springBootVersion=2.7.18
springAiVersion=0.8.1
```

#### 2.2 修改所有build.gradle文件

将Java版本改为8：
```gradle
java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}
```

**警告**: 这会导致：
- 无法使用Spring AI的最新特性
- 许多依赖可能不兼容
- 项目功能可能受限

### 方案3: 使用Docker（推荐给开发环境）

如果不想改变系统Java版本，可以使用Docker。

#### 3.1 创建Dockerfile

```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app
COPY . .

RUN chmod +x ./gradlew
RUN ./gradlew build --no-daemon

EXPOSE 8080
CMD ["./gradlew", "bootRun"]
```

#### 3.2 构建和运行

```cmd
docker build -t spring-ai-app .
docker run -p 8080:8080 spring-ai-app
```

## 推荐的完整解决步骤

### 步骤1: 安装Java 17

1. **下载Amazon Corretto 17**
2. **安装到**: `D:\Development\JDK\jdk-17`
3. **设置环境变量**:
   - JAVA_HOME = `D:\Development\JDK\jdk-17`
   - 更新PATH

### 步骤2: 验证Java安装

```cmd
# 重新打开命令行窗口
java -version
# 应该显示: openjdk version "17.x.x"

javac -version
# 应该显示: javac 17.x.x
```

### 步骤3: 修复项目配置

```cmd
# 1. 替换gradle.properties
copy gradle.properties.new gradle.properties

# 2. 移除foojay插件（已完成）
# settings.gradle中的插件已被注释

# 3. 清理缓存
./gradlew --stop
./gradlew clean --no-daemon

# 4. 测试构建
./gradlew :use-cases:chatbot:build --no-daemon
```

### 步骤4: 运行应用

```cmd
# 启动聊天机器人
./gradlew :use-cases:chatbot:bootRun

# 测试API
curl http://localhost:8080/chat/test-session -X POST -H "Content-Type: application/json" -d "\"Hello\""
```

## 一键解决脚本

创建 `fix-java-version.bat`：

```batch
@echo off
echo 检查Java版本...
java -version

echo.
echo 当前Java版本信息：
echo JAVA_HOME=%JAVA_HOME%

echo.
echo 如果显示的是Java 8，请：
echo 1. 下载并安装Java 17
echo 2. 设置JAVA_HOME环境变量
echo 3. 重新运行此脚本

echo.
echo 如果显示的是Java 17+，继续修复项目...

if exist gradle.properties.new (
    copy gradle.properties.new gradle.properties
    echo ✓ 已更新gradle.properties
)

echo 清理项目...
gradlew --stop
gradlew clean --no-daemon

echo 测试构建...
gradlew :use-cases:chatbot:build --no-daemon

if %ERRORLEVEL% EQU 0 (
    echo ✓ 构建成功！
    echo 现在可以运行: gradlew :use-cases:chatbot:bootRun
) else (
    echo ✗ 构建失败，请检查Java版本
)

pause
```

## 验证清单

完成修复后，验证以下内容：

- [ ] Java版本为17+: `java -version`
- [ ] JAVA_HOME正确设置
- [ ] Gradle构建成功: `./gradlew build --no-daemon`
- [ ] 应用可以启动: `./gradlew :use-cases:chatbot:bootRun`
- [ ] 没有IBM_SEMERU错误
- [ ] 没有SSL/TLS错误

## 常见问题

### Q: 安装Java 17后仍显示Java 8
**A**: 
1. 检查JAVA_HOME环境变量是否正确
2. 重启命令行窗口
3. 检查PATH中是否有多个Java路径

### Q: 企业环境无法安装新Java版本
**A**: 
1. 联系IT部门申请Java 17
2. 使用便携版Java（无需安装）
3. 考虑使用Docker方案

### Q: 构建仍然失败
**A**: 
1. 确认Java版本: `java -version`
2. 清理所有缓存: `./gradlew --stop && ./gradlew clean`
3. 检查网络连接和代理设置

## 总结

**最佳解决方案**: 升级到Java 17
- 这是唯一能完全解决问题的方案
- Spring Boot 3.x必须使用Java 17+
- 升级后项目将完全正常工作

**临时方案**: 使用Docker
- 适合不能改变系统Java版本的情况
- 隔离环境，不影响系统其他应用

**不推荐**: 降级Spring Boot版本
- 会失去大量新特性
- 可能引入其他兼容性问题
