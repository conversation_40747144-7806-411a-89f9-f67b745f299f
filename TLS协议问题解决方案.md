# TLS协议和Maven仓库问题解决方案

## 问题概述

在构建Spring AI项目时遇到了TLS协议版本不兼容的问题：

```
The server may not support the client's requested TLS protocol versions: (TLSv1.2, TLSv1.3). 
You may need to configure the client to allow other protocols to be used.
Remote host terminated the handshake
```

## 根本原因分析

1. **阿里云仓库地址问题**: 使用了旧的阿里云Maven仓库地址，该地址可能存在TLS配置问题
2. **TLS协议配置**: Gradle默认的TLS协议配置可能与某些Maven仓库不兼容
3. **仓库优先级**: 不稳定的仓库被优先使用，导致下载失败

## 解决方案

### 方案1: 更新仓库地址和配置（推荐）

#### 1.1 修改gradle.properties

已更新的配置：
```properties
# Gradle构建配置
org.gradle.configuration-cache=false
org.gradle.caching=true
org.gradle.parallel=true

# JVM参数配置 - 增加TLS协议支持
org.gradle.jvmargs=-Xmx8g -Dhttps.protocols=TLSv1.2,TLSv1.3 -Djdk.tls.client.protocols=TLSv1.2,TLSv1.3

# 依赖版本配置
arconiaVersion=0.12.0
jSpecifyVersion=1.0.0
springAiVersion=1.0.0
```

#### 1.2 优化仓库配置

在所有build.gradle文件中使用以下仓库配置：
```gradle
repositories {
    // 优先使用Maven中央仓库（最稳定）
    mavenCentral()
    
    // Spring官方仓库
    maven { 
        url 'https://repo.spring.io/snapshot'
        name 'Spring Snapshot Repository'
    }
    
    // 阿里云仓库（使用新地址）
    maven { 
        url 'https://maven.aliyun.com/repository/public'
        name 'Aliyun Public Repository'
    }
}
```

### 方案2: 禁用有问题的仓库

如果阿里云仓库仍有问题，可以暂时禁用：
```gradle
repositories {
    mavenCentral()
    maven { 
        url 'https://repo.spring.io/snapshot'
        name 'Spring Snapshot Repository'
    }
    // 暂时注释掉阿里云仓库
    // maven { url 'https://maven.aliyun.com/repository/public' }
}
```

### 方案3: 配置代理（如果在企业网络环境）

在gradle.properties中添加代理配置：
```properties
# HTTP代理配置
systemProp.http.proxyHost=proxy.company.com
systemProp.http.proxyPort=8080
systemProp.http.proxyUser=username
systemProp.http.proxyPassword=password

# HTTPS代理配置
systemProp.https.proxyHost=proxy.company.com
systemProp.https.proxyPort=8080
systemProp.https.proxyUser=username
systemProp.https.proxyPassword=password
```

## 批量修复脚本

### PowerShell脚本（Windows）

```powershell
# 批量修复所有build.gradle文件
Get-ChildItem -Recurse -Filter "build.gradle" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    
    # 替换旧的阿里云地址
    $content = $content -replace 'https://maven\.aliyun\.com/nexus/content/groups/public/', 'https://maven.aliyun.com/repository/public'
    
    # 移除buildscript和allprojects块
    $content = $content -replace 'buildscript\s*\{[^{}]*\{[^{}]*\}[^{}]*\}', ''
    $content = $content -replace 'allprojects\s*\{[^{}]*\{[^{}]*\}[^{}]*\}', ''
    
    Set-Content -Path $_.FullName -Value $content -Encoding UTF8
    Write-Host "Fixed: $($_.Name)"
}
```

### Bash脚本（Linux/Mac）

```bash
#!/bin/bash
# 批量修复所有build.gradle文件
find . -name "build.gradle" -type f | while read file; do
    # 替换旧的阿里云地址
    sed -i 's|https://maven\.aliyun\.com/nexus/content/groups/public/|https://maven.aliyun.com/repository/public|g' "$file"
    
    echo "Fixed: $(basename $file)"
done
```

## 验证解决方案

### 1. 清理Gradle缓存

```bash
# 清理Gradle缓存
./gradlew clean --no-daemon

# 删除Gradle缓存目录（可选）
rm -rf ~/.gradle/caches/
```

### 2. 测试构建

```bash
# 测试单个模块构建
./gradlew :use-cases:semantic-search:build --no-daemon

# 测试所有模块构建
./gradlew build --no-daemon
```

### 3. 检查网络连接

```bash
# 测试Maven中央仓库连接
curl -I https://repo1.maven.org/maven2/

# 测试Spring仓库连接
curl -I https://repo.spring.io/snapshot/

# 测试阿里云仓库连接
curl -I https://maven.aliyun.com/repository/public/
```

## 常见问题和解决方案

### Q1: 仍然出现TLS握手失败
**解决方案**:
1. 检查Java版本是否支持TLS 1.2/1.3
2. 更新JVM参数：`-Djdk.tls.client.protocols=TLSv1.2,TLSv1.3`
3. 考虑升级Java版本到最新的LTS版本

### Q2: 依赖下载速度慢
**解决方案**:
1. 调整仓库顺序，将最快的仓库放在前面
2. 使用本地Maven仓库镜像
3. 配置Gradle守护进程：`org.gradle.daemon=true`

### Q3: 企业防火墙阻止HTTPS连接
**解决方案**:
1. 配置企业代理服务器
2. 添加SSL证书到Java信任库
3. 联系网络管理员开放必要的端口

### Q4: 某些依赖无法下载
**解决方案**:
1. 检查依赖版本是否正确
2. 尝试使用不同的仓库
3. 手动下载依赖到本地仓库

## 最佳实践

### 1. 仓库配置优先级

```gradle
repositories {
    // 1. 本地仓库（最快）
    mavenLocal()
    
    // 2. Maven中央仓库（最稳定）
    mavenCentral()
    
    // 3. 官方项目仓库
    maven { url 'https://repo.spring.io/snapshot' }
    
    // 4. 镜像仓库（备用）
    maven { url 'https://maven.aliyun.com/repository/public' }
}
```

### 2. Gradle性能优化

```properties
# gradle.properties
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m
```

### 3. 网络配置

```properties
# 超时配置
systemProp.org.gradle.internal.http.connectionTimeout=60000
systemProp.org.gradle.internal.http.socketTimeout=60000

# TLS配置
systemProp.https.protocols=TLSv1.2,TLSv1.3
systemProp.jdk.tls.client.protocols=TLSv1.2,TLSv1.3
```

## 总结

通过以上解决方案，应该能够解决TLS协议和Maven仓库相关的构建问题。关键点：

1. **更新仓库地址**: 使用正确的HTTPS地址
2. **配置TLS协议**: 在JVM参数中明确指定支持的TLS版本
3. **优化仓库顺序**: 将最稳定的仓库放在前面
4. **清理缓存**: 删除可能损坏的Gradle缓存

如果问题仍然存在，建议检查网络环境和Java版本配置。
