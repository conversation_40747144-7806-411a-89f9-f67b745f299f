spring:
  application:
    name: observability-vector-stores-pgvector
  ai:
    chat:
      client:
        observations:
          log-prompt: true
      observations:
        log-completion: true
        log-prompt: true
    ollama:
      init:
        pull-model-strategy: when_missing
      chat:
        options:
          model: granite3.2:2b
          temperature: 0.7
      embedding:
        options:
          model: nomic-embed-text
    vectorstore:
      observations:
        log-query-response: true
      pgvector:
        initialize-schema: true
        dimensions: 768
        index-type: hnsw

management:
  endpoints:
    web:
      exposure:
        include: "*"

arconia:
  dev:
    services:
      postgresql:
        image-name: pgvector/pgvector:pg17
  otel:
    metrics:
      interval: 5s
