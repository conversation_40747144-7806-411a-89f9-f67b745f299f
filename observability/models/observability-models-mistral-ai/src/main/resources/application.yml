spring:
  application:
    name: observability-models-mistral-ai
  ai:
    chat:
      observations:
        log-completion: true
        log-prompt: true
    mistralai:
      api-key: ${MISTRALAI_API_KEY}
      chat:
        options:
          model: mistral-small-latest
          temperature: 0.7
      embedding:
        options:
          model: mistral-embed

management:
  endpoints:
    web:
      exposure:
        include: "*"
