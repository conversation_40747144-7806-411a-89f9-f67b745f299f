spring:
  application:
    name: observability-models-openai
  ai:
    chat:
      observations:
        log-completion: true
        log-prompt: true
    image:
      observations:
        log-prompt: true
    openai:
      api-key: ${OPENAI_API_KEY}
      chat:
        options:
          model: gpt-4o-mini
          temperature: 0.7
      embedding:
        options:
          model: text-embedding-3-small
      image:
        options:
          model: dall-e-3
  http:
    client:
      read-timeout: 60s

management:
  endpoints:
    web:
      exposure:
        include: "*"
