spring:
  application:
    name: observability-models-ollama
  ai:
    chat:
      observations:
        log-completion: true
        log-prompt: true
    ollama:
      init:
        pull-model-strategy: when_missing
      chat:
        options:
          model: granite3.2:2b
          temperature: 0.7
      embedding:
        options:
          model: nomic-embed-text

management:
  endpoints:
    web:
      exposure:
        include: "*"

arconia:
  otel:
    metrics:
      interval: 5s
