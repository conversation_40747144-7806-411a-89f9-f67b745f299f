# PowerShell脚本：批量修改所有build.gradle文件中的Java版本
# 将Java版本从24改为17，以兼容当前环境

Write-Host "开始修改Java版本配置..." -ForegroundColor Green

# 获取所有build.gradle文件
$buildFiles = Get-ChildItem -Recurse -Filter "build.gradle" -File

Write-Host "找到 $($buildFiles.Count) 个build.gradle文件" -ForegroundColor Yellow

$fixedCount = 0

foreach ($file in $buildFiles) {
    Write-Host "处理文件: $($file.Name)" -ForegroundColor Cyan
    
    try {
        # 读取文件内容
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        
        # 检查是否包含Java 24配置
        $hasJava24 = $content -match "languageVersion = JavaLanguageVersion\.of\(24\)"
        
        if ($hasJava24) {
            # 替换Java版本从24到17
            $content = $content -replace "languageVersion = JavaLanguageVersion\.of\(24\)", "languageVersion = JavaLanguageVersion.of(17)"
            
            # 写回文件
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            Write-Host "  ✓ 已修改Java版本: 24 → 17" -ForegroundColor Green
            $fixedCount++
        } else {
            Write-Host "  - 无需修改（未发现Java 24配置）" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "  ✗ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n修改完成！" -ForegroundColor Green
Write-Host "总文件数: $($buildFiles.Count)" -ForegroundColor Yellow
Write-Host "已修改: $fixedCount" -ForegroundColor Green

# 验证修改结果
Write-Host "`n正在验证修改结果..." -ForegroundColor Yellow
$remainingJava24Files = @()

foreach ($file in $buildFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    if ($content -match "languageVersion = JavaLanguageVersion\.of\(24\)") {
        $remainingJava24Files += $file.Name
    }
}

if ($remainingJava24Files.Count -eq 0) {
    Write-Host "✓ 所有文件的Java版本已成功修改为17！" -ForegroundColor Green
} else {
    Write-Host "⚠ 以下文件仍包含Java 24配置，需要手动检查：" -ForegroundColor Yellow
    foreach ($fileName in $remainingJava24Files) {
        Write-Host "  - $fileName" -ForegroundColor Red
    }
}

Write-Host "`n接下来的步骤：" -ForegroundColor Cyan
Write-Host "1. 确保已安装Java 17或更高版本" -ForegroundColor White
Write-Host "2. 设置JAVA_HOME环境变量指向Java 17" -ForegroundColor White
Write-Host "3. 运行: ./gradlew clean --no-daemon" -ForegroundColor White
Write-Host "4. 运行: ./gradlew build --no-daemon" -ForegroundColor White
