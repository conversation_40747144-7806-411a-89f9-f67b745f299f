# Spring AI 架构设计文档

## 整体架构概览

Spring AI 项目采用分层架构设计，将不同的关注点分离到不同的层次中，确保代码的可维护性和可扩展性。

```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        A[REST Controllers]
        B[Service Layer]
    end
    
    subgraph "Spring AI 框架层"
        C[ChatClient]
        D[Advisors]
        E[Document Processors]
    end
    
    subgraph "模型层 (Model Layer)"
        F[Chat Models]
        G[Embedding Models]
        H[Image Models]
    end
    
    subgraph "存储层 (Storage Layer)"
        I[Vector Store]
        J[Chat Memory]
        K[Document Store]
    end
    
    subgraph "外部服务"
        L[Ollama]
        M[OpenAI]
        N[Mistral AI]
        O[PostgreSQL + PGVector]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    D --> F
    D --> G
    E --> I
    F --> L
    F --> M
    F --> N
    I --> O
    J --> O
```

## 核心组件详解

### 1. ChatClient - 聊天客户端

`ChatClient` 是 Spring AI 的核心组件，提供了与大语言模型交互的高级 API。

**主要特性**:
- 流式和非流式响应支持
- 可配置的选项(温度、最大令牌数等)
- 支持多种消息类型(系统、用户、助手)
- 集成顾问(Advisor)模式

**使用示例**:
```java
@RestController
class ChatController {
    private final ChatClient chatClient;
    
    ChatController(ChatClient.Builder chatClientBuilder) {
        this.chatClient = chatClientBuilder
            .defaultOptions(ChatOptions.builder()
                .temperature(0.7)
                .build())
            .build();
    }
    
    @PostMapping("/chat")
    String chat(@RequestBody String message) {
        return chatClient
            .prompt(message)
            .call()
            .content();
    }
}
```

### 2. Advisor 模式 - 顾问组件

Advisor 是 Spring AI 中的一个重要设计模式，用于在 LLM 调用过程中注入额外的功能。

**常用 Advisor 类型**:

#### MessageChatMemoryAdvisor - 对话记忆顾问
```java
// 配置对话记忆
ChatClient chatClient = ChatClient.builder(chatModel)
    .defaultAdvisors(MessageChatMemoryAdvisor.builder(chatMemory).build())
    .build();

// 使用时指定会话ID
chatClient.prompt()
    .user(question)
    .advisors(a -> a.param(CONVERSATION_ID, conversationId))
    .call()
    .content();
```

#### RetrievalAugmentationAdvisor - 检索增强顾问
```java
// 配置RAG顾问
RetrievalAugmentationAdvisor ragAdvisor = RetrievalAugmentationAdvisor.builder()
    .documentRetriever(VectorStoreDocumentRetriever.builder()
        .vectorStore(vectorStore)
        .topK(5)
        .build())
    .build();

// 使用RAG进行问答
chatClient.prompt()
    .advisors(ragAdvisor)
    .user(question)
    .call()
    .content();
```

### 3. 向量存储架构

向量存储是 RAG 系统的核心组件，负责存储和检索文档的向量表示。

**架构组件**:
```java
// 文档摄取流程
@Component
class IngestionPipeline {
    
    @PostConstruct
    void ingestDocuments() {
        // 1. 读取文档
        List<Document> documents = textReader.get();
        
        // 2. 文档转换和分割
        List<Document> splitDocuments = textSplitter.split(documents);
        
        // 3. 生成嵌入向量并存储
        vectorStore.add(splitDocuments);
    }
}

// 检索流程
@Service
class RetrievalService {
    
    List<Document> retrieve(String query) {
        return vectorStore.similaritySearch(
            SearchRequest.builder()
                .query(query)
                .topK(5)
                .similarityThreshold(0.7)
                .build()
        );
    }
}
```

### 4. 文档处理管道

文档处理管道负责将原始文档转换为可搜索的向量表示。

**处理流程**:
1. **文档读取**: 支持多种格式(PDF, TXT, MD, JSON)
2. **文档转换**: 提取文本内容和元数据
3. **文档分割**: 将长文档分割成适合的块
4. **向量化**: 生成文档的嵌入向量
5. **存储**: 保存到向量数据库

```java
// 文档处理示例
@Component
class DocumentProcessor {
    
    void processDocument(Resource resource) {
        // 读取文档
        TextReader reader = new TextReader(resource);
        reader.getCustomMetadata().put("source", resource.getFilename());
        List<Document> documents = reader.get();
        
        // 分割文档
        TokenTextSplitter splitter = new TokenTextSplitter(500, 50);
        List<Document> chunks = splitter.split(documents);
        
        // 存储向量
        vectorStore.add(chunks);
    }
}
```

## 设计模式和最佳实践

### 1. 建造者模式 (Builder Pattern)

Spring AI 广泛使用建造者模式来配置复杂对象：

```java
// ChatClient 建造者
ChatClient chatClient = ChatClient.builder(chatModel)
    .defaultOptions(ChatOptions.builder()
        .model("gpt-4")
        .temperature(0.7)
        .maxTokens(1000)
        .build())
    .defaultAdvisors(
        MessageChatMemoryAdvisor.builder(chatMemory).build(),
        RetrievalAugmentationAdvisor.builder()
            .documentRetriever(retriever)
            .build()
    )
    .build();
```

### 2. 策略模式 (Strategy Pattern)

不同的模型提供商实现相同的接口：

```java
// 统一的聊天模型接口
public interface ChatModel {
    ChatResponse call(Prompt prompt);
}

// 不同的实现
@Component
class OllamaChatModel implements ChatModel { ... }

@Component  
class OpenAiChatModel implements ChatModel { ... }

@Component
class MistralAiChatModel implements ChatModel { ... }
```

### 3. 模板方法模式 (Template Method Pattern)

文档处理流程使用模板方法模式：

```java
abstract class DocumentProcessor {
    
    // 模板方法
    public final void process(Resource resource) {
        List<Document> documents = readDocument(resource);
        documents = transformDocument(documents);
        List<Document> chunks = splitDocument(documents);
        storeDocument(chunks);
    }
    
    protected abstract List<Document> readDocument(Resource resource);
    protected abstract List<Document> transformDocument(List<Document> documents);
    // ...
}
```

## 配置管理

### 1. 应用配置结构

```yaml
spring:
  ai:
    # 聊天模型配置
    chat:
      observations:
        log-completion: true
        log-prompt: true
    
    # Ollama 配置
    ollama:
      init:
        pull-model-strategy: when_missing
      chat:
        options:
          model: granite3.2:2b
          temperature: 0.7
          num-ctx: 4096
      embedding:
        options:
          model: nomic-embed-text
    
    # 向量存储配置
    vectorstore:
      observations:
        log-query-response: true
      pgvector:
        initialize-schema: true
        dimensions: 768
        index-type: hnsw
        distance-type: cosine

# 数据库配置
spring:
  datasource:
    url: *****************************************
    username: postgres
    password: password
```

### 2. 环境特定配置

```yaml
# application-dev.yml
spring:
  ai:
    ollama:
      base-url: http://localhost:11434
    
# application-prod.yml  
spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      chat:
        options:
          model: gpt-4-turbo
```

## 性能优化策略

### 1. 连接池配置

```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### 2. 缓存策略

```java
@Service
@EnableCaching
class CachedEmbeddingService {
    
    @Cacheable(value = "embeddings", key = "#text")
    public List<Double> getEmbedding(String text) {
        return embeddingModel.embed(text);
    }
}
```

### 3. 异步处理

```java
@Service
class AsyncDocumentProcessor {
    
    @Async
    @EventListener
    public void handleDocumentUpload(DocumentUploadEvent event) {
        processDocumentAsync(event.getDocument());
    }
    
    private void processDocumentAsync(Document document) {
        // 异步处理文档
    }
}
```

## 安全考虑

### 1. API 密钥管理

```java
@ConfigurationProperties(prefix = "spring.ai.openai")
public class OpenAiProperties {
    private String apiKey;
    // 使用环境变量或加密配置
}
```

### 2. 输入验证

```java
@RestController
@Validated
class ChatController {
    
    @PostMapping("/chat")
    String chat(@Valid @RequestBody ChatRequest request) {
        // 验证输入长度和内容
        if (request.getMessage().length() > 1000) {
            throw new IllegalArgumentException("Message too long");
        }
        return chatService.chat(request.getMessage());
    }
}
```

### 3. 输出过滤

```java
@Component
class ContentFilter {
    
    public String filterContent(String content) {
        // 过滤敏感内容
        return contentModerationService.filter(content);
    }
}
```

## 监控和可观测性

### 1. 指标收集

```java
@Component
class ChatMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter chatRequestCounter;
    private final Timer chatResponseTimer;
    
    public ChatMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.chatRequestCounter = Counter.builder("chat.requests")
            .description("Number of chat requests")
            .register(meterRegistry);
        this.chatResponseTimer = Timer.builder("chat.response.time")
            .description("Chat response time")
            .register(meterRegistry);
    }
}
```

### 2. 健康检查

```java
@Component
class LlmHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // 检查模型可用性
            chatModel.call("test");
            return Health.up()
                .withDetail("model", "available")
                .build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("model", "unavailable")
                .withException(e)
                .build();
        }
    }
}
```

## 测试策略

### 1. 单元测试

```java
@ExtendWith(MockitoExtension.class)
class ChatServiceTest {
    
    @Mock
    private ChatModel chatModel;
    
    @InjectMocks
    private ChatService chatService;
    
    @Test
    void shouldReturnResponse() {
        // 模拟LLM响应
        when(chatModel.call(any(Prompt.class)))
            .thenReturn(new ChatResponse(List.of(new Generation("test response"))));
        
        String response = chatService.chat("test question");
        
        assertThat(response).isEqualTo("test response");
    }
}
```

### 2. 集成测试

```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.ai.ollama.base-url=http://localhost:11434"
})
class ChatIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldHandleChatRequest() {
        String response = restTemplate.postForObject(
            "/chat", 
            "Hello", 
            String.class
        );
        
        assertThat(response).isNotEmpty();
    }
}
```

## 部署架构

### 1. 容器化部署

```dockerfile
FROM openjdk:24-jdk-slim

COPY target/app.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 2. Docker Compose 配置

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - postgres
      - ollama
  
  postgres:
    image: pgvector/pgvector:pg17
    environment:
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
  
  ollama:
    image: ollama/ollama
    ports:
      - "11434:11434"
```

### 3. Kubernetes 部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: spring-ai-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: spring-ai-app
  template:
    metadata:
      labels:
        app: spring-ai-app
    spec:
      containers:
      - name: app
        image: spring-ai-app:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes"
```

## 扩展指南

### 1. 添加新的模型提供商

1. 实现 `ChatModel` 接口
2. 创建配置属性类
3. 添加自动配置类
4. 编写测试用例

### 2. 自定义 Advisor

```java
@Component
public class CustomAdvisor implements RequestResponseAdvisor {
    
    @Override
    public AdvisedRequest adviseRequest(AdvisedRequest request, Map<String, Object> context) {
        // 请求预处理
        return request;
    }
    
    @Override
    public ChatResponse adviseResponse(ChatResponse response, Map<String, Object> context) {
        // 响应后处理
        return response;
    }
}
```

### 3. 自定义文档处理器

```java
@Component
public class CustomDocumentTransformer implements DocumentTransformer {
    
    @Override
    public List<Document> transform(List<Document> documents) {
        return documents.stream()
            .map(this::enhanceDocument)
            .collect(Collectors.toList());
    }
    
    private Document enhanceDocument(Document document) {
        // 自定义文档增强逻辑
        return document;
    }
}
```

这个架构设计确保了系统的可扩展性、可维护性和高性能，为构建企业级的 AI 应用提供了坚实的基础。
