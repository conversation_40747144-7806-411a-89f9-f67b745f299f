# Spring AI 架构设计文档

## 整体架构概览

Spring AI 项目采用分层架构设计，将不同的关注点分离到不同的层次中，确保代码的可维护性和可扩展性。整个架构遵循依赖倒置原则，通过接口抽象实现松耦合设计。

### 架构层次图

```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        A[REST Controllers]
        A1[ChatbotController]
        A2[SemanticSearchController]
        A3[ClassificationController]
        A4[StructuredDataExtractionController]
        A --> A1
        A --> A2
        A --> A3
        A --> A4
    end

    subgraph "业务逻辑层 (Business Logic Layer)"
        B[Service Layer]
        B1[ChatService]
        B2[RAGService]
        B3[IngestionService]
        B --> B1
        B --> B2
        B --> B3
    end

    subgraph "Spring AI 框架层 (Framework Layer)"
        C[ChatClient]
        D[Advisors]
        E[Document Processors]
        D1[MessageChatMemoryAdvisor]
        D2[RetrievalAugmentationAdvisor]
        E1[TextReader]
        E2[TokenTextSplitter]
        E3[DocumentTransformer]
        C --> D
        C --> E
        D --> D1
        D --> D2
        E --> E1
        E --> E2
        E --> E3
    end

    subgraph "模型抽象层 (Model Abstraction Layer)"
        F[Chat Models]
        G[Embedding Models]
        H[Image Models]
        I[Audio Models]
        F1[ChatModel Interface]
        G1[EmbeddingModel Interface]
        F --> F1
        G --> G1
    end

    subgraph "存储抽象层 (Storage Abstraction Layer)"
        J[Vector Store]
        K[Chat Memory]
        L[Document Store]
        J1[VectorStore Interface]
        K1[ChatMemory Interface]
        J --> J1
        K --> K1
    end

    subgraph "基础设施层 (Infrastructure Layer)"
        M[External Services]
        N[Databases]
        O[Configuration]
        M1[Ollama]
        M2[OpenAI]
        M3[Mistral AI]
        N1[PostgreSQL + PGVector]
        N2[Redis Cache]
        O1[Application Properties]
        O2[Security Config]
        M --> M1
        M --> M2
        M --> M3
        N --> N1
        N --> N2
        O --> O1
        O --> O2
    end

    A --> B
    B --> C
    C --> D
    C --> E
    D --> F
    D --> G
    E --> J
    F --> M
    G --> M
    J --> N
    K --> N
```

### 核心架构原则

1. **分层架构 (Layered Architecture)**:
   - 每层只依赖于下层，不跨层调用
   - 清晰的职责分离
   - 易于测试和维护

2. **依赖注入 (Dependency Injection)**:
   - 通过Spring容器管理依赖关系
   - 支持接口编程，降低耦合度
   - 便于单元测试和模拟

3. **策略模式 (Strategy Pattern)**:
   - 不同的模型提供商实现统一接口
   - 运行时动态选择实现
   - 易于扩展新的提供商

4. **建造者模式 (Builder Pattern)**:
   - 复杂对象的构建过程
   - 链式调用，提高代码可读性
   - 支持可选参数配置

5. **观察者模式 (Observer Pattern)**:
   - 可观测性和监控功能
   - 事件驱动的架构
   - 松耦合的组件通信

## 详细模块架构

### 1. Use Cases 模块架构

#### 1.1 聊天机器人模块 (Chatbot)

```mermaid
graph LR
    subgraph "聊天机器人架构"
        A[HTTP Request] --> B[ChatbotController]
        B --> C[ChatClient]
        C --> D[MessageChatMemoryAdvisor]
        D --> E[ChatMemory]
        C --> F[ChatModel]
        F --> G[Ollama/OpenAI/Mistral]
        E --> H[Database/Memory Store]
    end
```

**组件职责**:
- `ChatbotController`: HTTP请求处理，会话管理
- `ChatClient`: 高级聊天API，提示构建
- `MessageChatMemoryAdvisor`: 对话记忆管理
- `ChatMemory`: 对话历史存储抽象
- `ChatModel`: 模型调用抽象

#### 1.2 问答系统模块 (Question Answering)

```mermaid
graph TB
    subgraph "RAG问答系统架构"
        A[User Question] --> B[ChatController]
        B --> C[ChatClient]
        C --> D[RetrievalAugmentationAdvisor]
        D --> E[VectorStoreDocumentRetriever]
        E --> F[VectorStore]
        F --> G[PGVector Database]
        D --> H[DocumentJoiner]
        H --> I[LLM Context]
        I --> J[ChatModel]
        J --> K[Generated Answer]

        L[Document Ingestion] --> M[IngestionPipeline]
        M --> N[TextReader]
        N --> O[TokenTextSplitter]
        O --> P[EmbeddingModel]
        P --> F
    end
```

**数据流程**:
1. **摄取阶段**: 文档 → 读取 → 分割 → 向量化 → 存储
2. **检索阶段**: 查询 → 向量化 → 相似度搜索 → 文档检索
3. **生成阶段**: 检索结果 → 上下文构建 → LLM生成 → 答案返回

#### 1.3 语义搜索模块 (Semantic Search)

```mermaid
graph LR
    subgraph "语义搜索架构"
        A[Search Query] --> B[SemanticSearchController]
        B --> C[VectorStore]
        C --> D[Similarity Search]
        D --> E[Ranked Results]
        E --> F[InstrumentNote Objects]

        G[Data Ingestion] --> H[IngestionPipeline]
        H --> I[Document Creation]
        I --> J[Embedding Generation]
        J --> C
    end
```

### 2. Models 模块架构

#### 2.1 多提供商聊天模型架构

```mermaid
graph TB
    subgraph "多提供商架构"
        A[Application] --> B[ChatClient Factory]
        B --> C[Provider Selection]

        C --> D[OpenAI ChatClient]
        C --> E[Ollama ChatClient]
        C --> F[Mistral AI ChatClient]

        D --> G[OpenAI API]
        E --> H[Ollama Service]
        F --> I[Mistral AI API]

        J[Configuration] --> K[Provider Properties]
        K --> L[API Keys]
        K --> M[Model Settings]
        K --> N[Endpoint URLs]
    end
```

**配置管理**:
```java
// 多提供商配置
@Configuration
public class MultiProviderConfig {

    @Bean
    @ConditionalOnProperty("spring.ai.openai.api-key")
    public OpenAiChatModel openAiChatModel() {
        return new OpenAiChatModel(openAiApi, openAiChatOptions);
    }

    @Bean
    @ConditionalOnProperty("spring.ai.ollama.base-url")
    public OllamaChatModel ollamaChatModel() {
        return new OllamaChatModel(ollamaApi, ollamaChatOptions);
    }

    @Bean
    public ChatClient.Builder chatClientBuilder(List<ChatModel> chatModels) {
        return ChatClient.builder()
            .defaultModel(selectPrimaryModel(chatModels));
    }
}
```

#### 2.2 嵌入模型架构

```mermaid
graph LR
    subgraph "嵌入模型架构"
        A[Text Input] --> B[EmbeddingModel]
        B --> C[Provider Selection]

        C --> D[OpenAI Embeddings]
        C --> E[Ollama Embeddings]
        C --> F[Transformers Embeddings]

        D --> G[text-embedding-ada-002]
        E --> H[nomic-embed-text]
        F --> I[ONNX Models]

        J[Vector Output] --> K[VectorStore]
    end
```

### 3. Patterns 模块架构

#### 3.1 Advisor 模式架构

```mermaid
graph TB
    subgraph "Advisor模式架构"
        A[ChatClient Request] --> B[Advisor Chain]

        B --> C[Pre-Request Advisors]
        C --> D[MessageChatMemoryAdvisor]
        C --> E[RetrievalAugmentationAdvisor]
        C --> F[Custom Advisors]

        B --> G[Request Processing]
        G --> H[ChatModel Call]

        H --> I[Post-Response Advisors]
        I --> J[Response Filtering]
        I --> K[Logging Advisor]
        I --> L[Metrics Advisor]

        L --> M[Final Response]
    end
```

**Advisor接口设计**:
```java
public interface RequestResponseAdvisor {

    // 请求预处理
    AdvisedRequest adviseRequest(AdvisedRequest request, Map<String, Object> context);

    // 响应后处理
    ChatResponse adviseResponse(ChatResponse response, Map<String, Object> context);

    // 顾问名称
    String getName();

    // 执行顺序
    int getOrder();
}
```

#### 3.2 工具调用架构

```mermaid
graph LR
    subgraph "工具调用架构"
        A[User Request] --> B[ChatClient]
        B --> C[Tool Detection]
        C --> D[Function Registry]
        D --> E[Tool Execution]

        E --> F[Weather Service]
        E --> G[Calculator Service]
        E --> H[Database Service]

        F --> I[External API]
        G --> J[Local Computation]
        H --> K[Database Query]

        I --> L[Tool Results]
        J --> L
        K --> L
        L --> M[Response Generation]
    end
```

### 4. RAG 模块架构

#### 4.1 顺序RAG架构

```mermaid
graph TB
    subgraph "顺序RAG架构"
        A[User Query] --> B[Query Processing]
        B --> C[Vector Search]
        C --> D[Document Retrieval]
        D --> E[Context Assembly]
        E --> F[Prompt Construction]
        F --> G[LLM Generation]
        G --> H[Response]

        I[Document Store] --> C
        J[Embedding Model] --> B
        J --> K[Document Indexing]
        K --> I
    end
```

#### 4.2 分支RAG架构

```mermaid
graph TB
    subgraph "分支RAG架构"
        A[User Query] --> B[Query Expansion]
        B --> C[Multiple Queries]

        C --> D[Vector Search 1]
        C --> E[Vector Search 2]
        C --> F[Vector Search 3]

        D --> G[Results 1]
        E --> H[Results 2]
        F --> I[Results 3]

        G --> J[Result Aggregation]
        H --> J
        I --> J

        J --> K[Context Assembly]
        K --> L[LLM Generation]
    end
```

#### 4.3 条件RAG架构

```mermaid
graph TB
    subgraph "条件RAG架构"
        A[User Query] --> B[Intent Classification]
        B --> C[Routing Decision]

        C --> D[Vector Store Route]
        C --> E[Search Engine Route]
        C --> F[Database Route]

        D --> G[Semantic Search]
        E --> H[Keyword Search]
        F --> I[Structured Query]

        G --> J[Context Assembly]
        H --> J
        I --> J

        J --> K[LLM Generation]
    end
```

## 核心组件详解

### 1. ChatClient - 聊天客户端

`ChatClient` 是 Spring AI 的核心组件，提供了与大语言模型交互的高级 API。

**架构设计**:
```java
public interface ChatClient {

    // 流式构建器
    interface Builder {
        Builder defaultModel(ChatModel chatModel);
        Builder defaultOptions(ChatOptions chatOptions);
        Builder defaultAdvisors(RequestResponseAdvisor... advisors);
        Builder defaultSystem(String systemText);
        ChatClient build();
    }

    // 提示构建器
    interface PromptSpec {
        PromptSpec system(String systemText);
        PromptSpec user(String userText);
        PromptSpec options(ChatOptions options);
        PromptSpec advisors(RequestResponseAdvisor... advisors);
        CallSpec call();
        StreamSpec stream();
    }
}
```

**主要特性**:
- 流式和非流式响应支持
- 可配置的选项(温度、最大令牌数等)
- 支持多种消息类型(系统、用户、助手)
- 集成顾问(Advisor)模式
- 链式调用API设计
- 类型安全的构建器模式

**使用示例**:
```java
@RestController
class ChatController {
    private final ChatClient chatClient;

    ChatController(ChatClient.Builder chatClientBuilder) {
        this.chatClient = chatClientBuilder
            .defaultOptions(ChatOptions.builder()
                .temperature(0.7)
                .maxTokens(1000)
                .build())
            .defaultAdvisors(
                MessageChatMemoryAdvisor.builder(chatMemory).build()
            )
            .build();
    }

    @PostMapping("/chat")
    String chat(@RequestBody String message) {
        return chatClient
            .prompt()
            .system("你是一个有帮助的助手")
            .user(message)
            .call()
            .content();
    }

    @PostMapping("/chat/stream")
    Flux<String> chatStream(@RequestBody String message) {
        return chatClient
            .prompt()
            .user(message)
            .stream()
            .content();
    }
}
```

### 2. Advisor 模式 - 顾问组件

Advisor 是 Spring AI 中的一个重要设计模式，用于在 LLM 调用过程中注入额外的功能。

**常用 Advisor 类型**:

#### MessageChatMemoryAdvisor - 对话记忆顾问
```java
// 配置对话记忆
ChatClient chatClient = ChatClient.builder(chatModel)
    .defaultAdvisors(MessageChatMemoryAdvisor.builder(chatMemory).build())
    .build();

// 使用时指定会话ID
chatClient.prompt()
    .user(question)
    .advisors(a -> a.param(CONVERSATION_ID, conversationId))
    .call()
    .content();
```

#### RetrievalAugmentationAdvisor - 检索增强顾问
```java
// 配置RAG顾问
RetrievalAugmentationAdvisor ragAdvisor = RetrievalAugmentationAdvisor.builder()
    .documentRetriever(VectorStoreDocumentRetriever.builder()
        .vectorStore(vectorStore)
        .topK(5)
        .build())
    .build();

// 使用RAG进行问答
chatClient.prompt()
    .advisors(ragAdvisor)
    .user(question)
    .call()
    .content();
```

### 3. 向量存储架构

向量存储是 RAG 系统的核心组件，负责存储和检索文档的向量表示。

**架构组件**:
```java
// 文档摄取流程
@Component
class IngestionPipeline {
    
    @PostConstruct
    void ingestDocuments() {
        // 1. 读取文档
        List<Document> documents = textReader.get();
        
        // 2. 文档转换和分割
        List<Document> splitDocuments = textSplitter.split(documents);
        
        // 3. 生成嵌入向量并存储
        vectorStore.add(splitDocuments);
    }
}

// 检索流程
@Service
class RetrievalService {
    
    List<Document> retrieve(String query) {
        return vectorStore.similaritySearch(
            SearchRequest.builder()
                .query(query)
                .topK(5)
                .similarityThreshold(0.7)
                .build()
        );
    }
}
```

### 4. 文档处理管道

文档处理管道负责将原始文档转换为可搜索的向量表示。

**处理流程**:
1. **文档读取**: 支持多种格式(PDF, TXT, MD, JSON)
2. **文档转换**: 提取文本内容和元数据
3. **文档分割**: 将长文档分割成适合的块
4. **向量化**: 生成文档的嵌入向量
5. **存储**: 保存到向量数据库

```java
// 文档处理示例
@Component
class DocumentProcessor {
    
    void processDocument(Resource resource) {
        // 读取文档
        TextReader reader = new TextReader(resource);
        reader.getCustomMetadata().put("source", resource.getFilename());
        List<Document> documents = reader.get();
        
        // 分割文档
        TokenTextSplitter splitter = new TokenTextSplitter(500, 50);
        List<Document> chunks = splitter.split(documents);
        
        // 存储向量
        vectorStore.add(chunks);
    }
}
```

## 设计模式和最佳实践

### 1. 建造者模式 (Builder Pattern)

Spring AI 广泛使用建造者模式来配置复杂对象：

```java
// ChatClient 建造者
ChatClient chatClient = ChatClient.builder(chatModel)
    .defaultOptions(ChatOptions.builder()
        .model("gpt-4")
        .temperature(0.7)
        .maxTokens(1000)
        .build())
    .defaultAdvisors(
        MessageChatMemoryAdvisor.builder(chatMemory).build(),
        RetrievalAugmentationAdvisor.builder()
            .documentRetriever(retriever)
            .build()
    )
    .build();
```

### 2. 策略模式 (Strategy Pattern)

不同的模型提供商实现相同的接口：

```java
// 统一的聊天模型接口
public interface ChatModel {
    ChatResponse call(Prompt prompt);
}

// 不同的实现
@Component
class OllamaChatModel implements ChatModel { ... }

@Component  
class OpenAiChatModel implements ChatModel { ... }

@Component
class MistralAiChatModel implements ChatModel { ... }
```

### 3. 模板方法模式 (Template Method Pattern)

文档处理流程使用模板方法模式：

```java
abstract class DocumentProcessor {
    
    // 模板方法
    public final void process(Resource resource) {
        List<Document> documents = readDocument(resource);
        documents = transformDocument(documents);
        List<Document> chunks = splitDocument(documents);
        storeDocument(chunks);
    }
    
    protected abstract List<Document> readDocument(Resource resource);
    protected abstract List<Document> transformDocument(List<Document> documents);
    // ...
}
```

## 配置管理

### 1. 应用配置结构

```yaml
spring:
  ai:
    # 聊天模型配置
    chat:
      observations:
        log-completion: true
        log-prompt: true
    
    # Ollama 配置
    ollama:
      init:
        pull-model-strategy: when_missing
      chat:
        options:
          model: granite3.2:2b
          temperature: 0.7
          num-ctx: 4096
      embedding:
        options:
          model: nomic-embed-text
    
    # 向量存储配置
    vectorstore:
      observations:
        log-query-response: true
      pgvector:
        initialize-schema: true
        dimensions: 768
        index-type: hnsw
        distance-type: cosine

# 数据库配置
spring:
  datasource:
    url: *****************************************
    username: postgres
    password: password
```

### 2. 环境特定配置

```yaml
# application-dev.yml
spring:
  ai:
    ollama:
      base-url: http://localhost:11434
    
# application-prod.yml  
spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      chat:
        options:
          model: gpt-4-turbo
```

## 性能优化策略

### 1. 连接池配置

```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### 2. 缓存策略

```java
@Service
@EnableCaching
class CachedEmbeddingService {
    
    @Cacheable(value = "embeddings", key = "#text")
    public List<Double> getEmbedding(String text) {
        return embeddingModel.embed(text);
    }
}
```

### 3. 异步处理

```java
@Service
class AsyncDocumentProcessor {
    
    @Async
    @EventListener
    public void handleDocumentUpload(DocumentUploadEvent event) {
        processDocumentAsync(event.getDocument());
    }
    
    private void processDocumentAsync(Document document) {
        // 异步处理文档
    }
}
```

## 安全考虑

### 1. API 密钥管理

```java
@ConfigurationProperties(prefix = "spring.ai.openai")
public class OpenAiProperties {
    private String apiKey;
    // 使用环境变量或加密配置
}
```

### 2. 输入验证

```java
@RestController
@Validated
class ChatController {
    
    @PostMapping("/chat")
    String chat(@Valid @RequestBody ChatRequest request) {
        // 验证输入长度和内容
        if (request.getMessage().length() > 1000) {
            throw new IllegalArgumentException("Message too long");
        }
        return chatService.chat(request.getMessage());
    }
}
```

### 3. 输出过滤

```java
@Component
class ContentFilter {
    
    public String filterContent(String content) {
        // 过滤敏感内容
        return contentModerationService.filter(content);
    }
}
```

## 监控和可观测性

### 1. 指标收集

```java
@Component
class ChatMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter chatRequestCounter;
    private final Timer chatResponseTimer;
    
    public ChatMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.chatRequestCounter = Counter.builder("chat.requests")
            .description("Number of chat requests")
            .register(meterRegistry);
        this.chatResponseTimer = Timer.builder("chat.response.time")
            .description("Chat response time")
            .register(meterRegistry);
    }
}
```

### 2. 健康检查

```java
@Component
class LlmHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // 检查模型可用性
            chatModel.call("test");
            return Health.up()
                .withDetail("model", "available")
                .build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("model", "unavailable")
                .withException(e)
                .build();
        }
    }
}
```

## 测试策略

### 1. 单元测试

```java
@ExtendWith(MockitoExtension.class)
class ChatServiceTest {
    
    @Mock
    private ChatModel chatModel;
    
    @InjectMocks
    private ChatService chatService;
    
    @Test
    void shouldReturnResponse() {
        // 模拟LLM响应
        when(chatModel.call(any(Prompt.class)))
            .thenReturn(new ChatResponse(List.of(new Generation("test response"))));
        
        String response = chatService.chat("test question");
        
        assertThat(response).isEqualTo("test response");
    }
}
```

### 2. 集成测试

```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.ai.ollama.base-url=http://localhost:11434"
})
class ChatIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldHandleChatRequest() {
        String response = restTemplate.postForObject(
            "/chat", 
            "Hello", 
            String.class
        );
        
        assertThat(response).isNotEmpty();
    }
}
```

## 部署架构

### 1. 容器化部署

```dockerfile
FROM openjdk:24-jdk-slim

COPY target/app.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 2. Docker Compose 配置

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - postgres
      - ollama
  
  postgres:
    image: pgvector/pgvector:pg17
    environment:
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
  
  ollama:
    image: ollama/ollama
    ports:
      - "11434:11434"
```

### 3. Kubernetes 部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: spring-ai-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: spring-ai-app
  template:
    metadata:
      labels:
        app: spring-ai-app
    spec:
      containers:
      - name: app
        image: spring-ai-app:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes"
```

## 扩展指南

### 1. 添加新的模型提供商

1. 实现 `ChatModel` 接口
2. 创建配置属性类
3. 添加自动配置类
4. 编写测试用例

### 2. 自定义 Advisor

```java
@Component
public class CustomAdvisor implements RequestResponseAdvisor {
    
    @Override
    public AdvisedRequest adviseRequest(AdvisedRequest request, Map<String, Object> context) {
        // 请求预处理
        return request;
    }
    
    @Override
    public ChatResponse adviseResponse(ChatResponse response, Map<String, Object> context) {
        // 响应后处理
        return response;
    }
}
```

### 3. 自定义文档处理器

```java
@Component
public class CustomDocumentTransformer implements DocumentTransformer {
    
    @Override
    public List<Document> transform(List<Document> documents) {
        return documents.stream()
            .map(this::enhanceDocument)
            .collect(Collectors.toList());
    }
    
    private Document enhanceDocument(Document document) {
        // 自定义文档增强逻辑
        return document;
    }
}
```

这个架构设计确保了系统的可扩展性、可维护性和高性能，为构建企业级的 AI 应用提供了坚实的基础。
