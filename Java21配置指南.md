# Spring AI 项目 Java 21 配置指南

## 概述

既然您本地安装了Java 21，我来帮您将整个项目配置为使用Java 21。Java 21是一个LTS版本，完全兼容Spring Boot 3.4.6和Spring AI 1.0。

## 当前状态

- ✅ **Java 21已安装**
- ✅ **Spring Boot 3.4.6** (支持Java 21)
- ✅ **Gradle 8.14.1** (支持Java 21)

## 配置步骤

### 步骤1: 验证Java 21安装

```cmd
java -version
```

应该显示类似：
```
openjdk version "21.0.x" 2023-xx-xx LTS
```

如果显示的不是Java 21，请检查JAVA_HOME环境变量：
```cmd
echo %JAVA_HOME%
```

### 步骤2: 批量更新项目配置

我已经为您准备了自动化脚本：

```cmd
# 运行批量更新脚本
update-to-java21.bat
```

这个脚本会：
1. 检查Java版本
2. 将所有build.gradle文件中的Java配置更新为21
3. 清理Gradle缓存
4. 测试构建

### 步骤3: 手动验证关键配置

#### 3.1 检查gradle.properties

替换当前的gradle.properties：
```cmd
copy gradle-java21.properties gradle.properties
```

#### 3.2 验证build.gradle配置

所有模块的build.gradle应该包含：
```gradle
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
        nativeImageCapable = true
    }
}
```

### 步骤4: IntelliJ IDEA配置

#### 4.1 项目SDK设置
1. **File → Project Structure → Project**
2. **Project SDK**: 选择Java 21
3. **Project language level**: 21

#### 4.2 Gradle设置
1. **File → Settings → Build → Gradle**
2. **Gradle JVM**: 选择Java 21
3. **Use Gradle from**: 'gradle-wrapper.properties' file

#### 4.3 刷新项目
1. **File → Reload Gradle Project**
2. 或者 **View → Tool Windows → Gradle → 刷新按钮**

## Java 21的优势

### 性能提升
- **更好的GC性能**: ZGC和G1GC的改进
- **启动时间优化**: 更快的应用启动
- **内存使用优化**: 更高效的内存管理

### 新特性支持
- **Virtual Threads**: 轻量级并发
- **Pattern Matching**: 更简洁的代码
- **Record Patterns**: 增强的数据处理
- **String Templates**: 更安全的字符串处理

### Spring AI兼容性
- **完全支持**: Spring AI 1.0原生支持Java 21
- **性能优化**: 利用Java 21的新特性提升AI应用性能
- **GraalVM支持**: 更好的原生镜像编译

## 验证配置

### 构建测试
```cmd
# 清理并重新构建
./gradlew clean build --no-daemon

# 测试单个模块
./gradlew :use-cases:chatbot:build --no-daemon
```

### 运行应用
```cmd
# 启动聊天机器人
./gradlew :use-cases:chatbot:bootRun

# 测试API
curl http://localhost:8080/chat/test-session -X POST -H "Content-Type: application/json" -d "\"Hello from Java 21!\""
```

## 故障排除

### 问题1: 仍然显示旧的Java版本
**解决方案**:
1. 检查JAVA_HOME环境变量
2. 重启命令行窗口
3. 在IntelliJ IDEA中重新导入项目

### 问题2: Gradle构建失败
**解决方案**:
```cmd
# 停止所有Gradle守护进程
./gradlew --stop

# 清理缓存
./gradlew clean --no-daemon

# 删除.gradle目录
rmdir /s .gradle

# 重新构建
./gradlew build --no-daemon
```

### 问题3: IntelliJ IDEA仍然报错
**解决方案**:
1. **File → Invalidate Caches and Restart**
2. 重新导入项目：**File → New → Project from Existing Sources**
3. 选择项目根目录的build.gradle文件

## 性能优化建议

### JVM参数优化
在gradle.properties中：
```properties
# 针对Java 21优化的JVM参数
org.gradle.jvmargs=-Xmx8g -XX:+UseZGC -XX:+UnlockExperimentalVMOptions
```

### Gradle配置优化
```properties
# 启用并行构建
org.gradle.parallel=true
org.gradle.caching=true

# 配置守护进程
org.gradle.daemon=true
```

## 验证清单

配置完成后，确认以下内容：

- [ ] `java -version` 显示Java 21
- [ ] 所有build.gradle文件使用Java 21配置
- [ ] Gradle构建成功
- [ ] IntelliJ IDEA项目结构正确
- [ ] 应用可以正常启动
- [ ] 没有工具链相关错误

## 总结

Java 21是Spring AI项目的理想选择：
- **LTS版本**: 长期支持，稳定可靠
- **性能优异**: 相比Java 8有显著提升
- **完全兼容**: 支持所有Spring AI功能
- **未来保证**: 支持最新的Java特性

通过以上配置，您的Spring AI项目将充分利用Java 21的优势，获得最佳的开发和运行体验。
