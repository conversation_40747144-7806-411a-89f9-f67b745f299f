# PowerShell脚本：修复Java工具链配置问题
# 将复杂的toolchain配置替换为简单的sourceCompatibility配置

Write-Host "开始修复Java工具链配置问题..." -ForegroundColor Green

# 获取所有build.gradle文件
$buildFiles = Get-ChildItem -Recurse -Filter "build.gradle" -File

Write-Host "找到 $($buildFiles.Count) 个build.gradle文件" -ForegroundColor Yellow

$fixedCount = 0

foreach ($file in $buildFiles) {
    Write-Host "处理文件: $($file.Name)" -ForegroundColor Cyan
    
    try {
        # 读取文件内容
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        $hasChanges = $false
        
        # 检查是否包含toolchain配置
        if ($content -match "toolchain\s*\{") {
            Write-Host "  发现toolchain配置，正在替换..." -ForegroundColor Yellow
            
            # 替换复杂的toolchain配置为简单的兼容性配置
            $toolchainPattern = "java\s*\{\s*toolchain\s*\{[^}]*\}[^}]*\}"
            $newJavaConfig = @"
java {
    // 简化Java配置以避免工具链问题
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}
"@
            
            $content = $content -replace $toolchainPattern, $newJavaConfig
            $hasChanges = $true
            Write-Host "  ✓ 已替换toolchain配置" -ForegroundColor Green
        }
        
        # 如果有变化则保存文件
        if ($hasChanges) {
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            $fixedCount++
            Write-Host "  ✓ 文件已修复" -ForegroundColor Green
        } else {
            Write-Host "  - 无需修复" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "  ✗ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n修复完成！" -ForegroundColor Green
Write-Host "总文件数: $($buildFiles.Count)" -ForegroundColor Yellow
Write-Host "已修复: $fixedCount" -ForegroundColor Green

Write-Host "`n接下来的步骤：" -ForegroundColor Cyan
Write-Host "1. 在IntelliJ IDEA中刷新Gradle项目" -ForegroundColor White
Write-Host "2. 或运行: ./gradlew clean build --no-daemon" -ForegroundColor White
