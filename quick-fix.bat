@echo off
echo 快速修复Spring AI项目构建问题...

echo 1. 修复Java版本配置...
powershell -Command "Get-ChildItem -Recurse -Filter 'build.gradle' | ForEach-Object { $c = Get-Content $_.FullName -Raw; $c = $c -replace 'languageVersion = JavaLanguageVersion\.of\(24\)', 'languageVersion = JavaLanguageVersion.of(17)'; Set-Content $_.FullName $c -Encoding UTF8 }"

echo 2. 修复阿里云仓库地址...
powershell -Command "Get-ChildItem -Recurse -Filter 'build.gradle' | ForEach-Object { $c = Get-Content $_.FullName -Raw; $c = $c -replace 'https://maven\.aliyun\.com/nexus/content/groups/public/', 'https://maven.aliyun.com/repository/public'; Set-Content $_.FullName $c -Encoding UTF8 }"

echo 3. 清理Gradle缓存...
gradlew clean --no-daemon

echo 4. 停止Gradle守护进程...
gradlew --stop

echo 5. 测试构建...
gradlew :use-cases:chatbot:build --no-daemon

echo 修复完成！
pause
