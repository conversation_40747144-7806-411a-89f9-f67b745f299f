spring:
  ai:
    mcp:
      client:
        stdio:
          connections:
            brave-search:
              command: npx
              args:
                - "-y"
                - "@modelcontextprotocol/server-brave-search"
              env:
                BRAVE_API_KEY: ${BRAVE_API_KEY}
    ollama:
      init:
        pull-model-strategy: when_missing
        embedding:
          include: false
      chat:
        options:
          model: granite3.2:2b
          temperature: 0.7
