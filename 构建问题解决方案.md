# Spring AI 项目构建问题解决方案

## 问题概述

在运行Spring AI项目时遇到了两个主要问题：

1. **HTTP仓库协议问题**: Gradle 9.0+版本不再允许使用不安全的HTTP协议访问Maven仓库
2. **Java版本兼容性问题**: Spring Boot 3.4.6需要Java 17+，但系统当前使用Java 8

## 问题1: HTTP仓库协议问题

### 错误信息
```
Using insecure protocols with repositories, without explicit opt-in, is unsupported.
Switch Maven repository 'maven(http://maven.aliyun.com/nexus/content/groups/public/)' to redirect to a secure protocol (like HTTPS)
```

### 解决方案

#### 方案1: 修改仓库地址为HTTPS（推荐）

将所有build.gradle文件中的HTTP仓库地址替换为HTTPS：

**原地址**:
```gradle
maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
maven { url 'http://repo.spring.io/snapshot' }
```

**修改为**:
```gradle
maven { 
    url 'https://maven.aliyun.com/repository/public'
    name 'Aliyun Public Repository'
}
maven { 
    url 'https://repo.spring.io/snapshot'
    name 'Spring Snapshot Repository'
}
```

#### 方案2: 允许不安全协议（不推荐）

在build.gradle中添加：
```gradle
repositories {
    maven {
        url 'http://maven.aliyun.com/nexus/content/groups/public/'
        allowInsecureProtocol = true
    }
}
```

### 批量修复脚本

创建PowerShell脚本批量修复所有build.gradle文件：

```powershell
# 查找所有build.gradle文件并替换HTTP为HTTPS
Get-ChildItem -Recurse -Filter "build.gradle" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    $original = $content
    
    # 替换阿里云仓库地址
    $content = $content -replace 'http://maven\.aliyun\.com/nexus/content/groups/public/', 'https://maven.aliyun.com/repository/public'
    
    # 替换Spring仓库地址
    $content = $content -replace 'http://repo\.spring\.io/snapshot', 'https://repo.spring.io/snapshot'
    $content = $content -replace 'http://repo\.spring\.io/milestone', 'https://repo.spring.io/milestone'
    
    # 如果有变化则保存
    if ($content -ne $original) {
        Set-Content -Path $_.FullName -Value $content -Encoding UTF8
        Write-Host "已修复: $($_.Name)" -ForegroundColor Green
    }
}
```

## 问题2: Java版本兼容性问题

### 错误信息
```
Dependency requires at least JVM runtime version 17. This build uses a Java 8 JVM.
```

### 当前环境
- 系统Java版本: Java 8 (1.8.0_101)
- JAVA_HOME: D:\Development\JDK\jdk1.8.0_101
- 项目要求: Java 17+

### 解决方案

#### 方案1: 升级Java版本（推荐）

1. **下载并安装Java 17或更高版本**:
   - Oracle JDK: https://www.oracle.com/java/technologies/downloads/
   - OpenJDK: https://adoptium.net/
   - Amazon Corretto: https://aws.amazon.com/corretto/

2. **更新环境变量**:
   ```cmd
   # 设置JAVA_HOME
   set JAVA_HOME=D:\Development\JDK\jdk-17
   
   # 更新PATH
   set PATH=%JAVA_HOME%\bin;%PATH%
   ```

3. **验证安装**:
   ```cmd
   java -version
   javac -version
   ```

#### 方案2: 使用Gradle工具链（推荐）

在gradle.properties中配置：
```properties
# 指定Java工具链版本
org.gradle.java.home=D:/Development/JDK/jdk-17
```

或在build.gradle中配置：
```gradle
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}
```

#### 方案3: 降级Spring Boot版本（不推荐）

修改gradle.properties中的Spring Boot版本：
```properties
# 从 3.4.6 降级到支持Java 8的版本
springBootVersion=2.7.18
```

**注意**: 这会导致无法使用Spring AI的最新特性。

## 完整解决步骤

### 步骤1: 修复HTTP仓库问题

1. 运行PowerShell脚本批量修复仓库地址
2. 或手动修改关键的build.gradle文件

### 步骤2: 解决Java版本问题

1. **安装Java 17+**:
   ```cmd
   # 下载并安装Java 17
   # 例如: Amazon Corretto 17
   ```

2. **更新环境变量**:
   ```cmd
   # Windows系统设置中更新JAVA_HOME
   JAVA_HOME=D:\Development\JDK\jdk-17
   ```

3. **验证配置**:
   ```cmd
   java -version
   # 应该显示Java 17或更高版本
   ```

### 步骤3: 测试构建

```cmd
# 清理并重新构建
./gradlew clean build --no-daemon

# 或构建特定模块
./gradlew :use-cases:chatbot:build --no-daemon
```

## 推荐的开发环境配置

### Java版本管理

使用SDKMAN（Linux/Mac）或Scoop（Windows）管理多个Java版本：

```cmd
# Windows使用Scoop
scoop install openjdk17
scoop install openjdk21

# 切换Java版本
scoop reset openjdk17
```

### IDE配置

#### IntelliJ IDEA
1. File → Project Structure → Project Settings → Project
2. 设置Project SDK为Java 17+
3. 设置Project language level为17或更高

#### VS Code
1. 安装Extension Pack for Java
2. 配置settings.json:
   ```json
   {
     "java.configuration.runtimes": [
       {
         "name": "JavaSE-17",
         "path": "D:/Development/JDK/jdk-17"
       }
     ]
   }
   ```

### Gradle配置优化

在gradle.properties中添加：
```properties
# 使用Gradle守护进程
org.gradle.daemon=true

# 并行构建
org.gradle.parallel=true

# 配置JVM参数
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m

# 指定Java版本
org.gradle.java.home=D:/Development/JDK/jdk-17
```

## 验证解决方案

运行以下命令验证问题是否解决：

```cmd
# 检查Java版本
java -version

# 检查Gradle版本
./gradlew --version

# 构建项目
./gradlew build --no-daemon

# 运行特定用例
./gradlew :use-cases:chatbot:bootRun
```

## 常见问题

### Q: 修改后仍然报HTTP错误
A: 检查是否有遗漏的build.gradle文件，确保所有HTTP地址都已替换为HTTPS。

### Q: Java版本更新后IDE仍然使用旧版本
A: 重启IDE并检查项目设置中的SDK配置。

### Q: Gradle构建速度慢
A: 配置Gradle守护进程和并行构建，使用本地Maven仓库缓存。

### Q: 依赖下载失败
A: 检查网络连接，考虑使用国内镜像仓库或配置代理。

通过以上步骤，应该能够成功解决Spring AI项目的构建问题。
