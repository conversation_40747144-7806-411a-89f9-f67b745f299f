@echo off
echo 修复IBM_SEMERU错误...

echo 1. 停止Gradle守护进程...
gradlew --stop

echo 2. 清理项目缓存...
gradlew clean --no-daemon

echo 3. 删除Gradle缓存...
if exist "%USERPROFILE%\.gradle\caches" (
    rmdir /s /q "%USERPROFILE%\.gradle\caches"
    echo Gradle缓存已清理
)

echo 4. 测试构建...
gradlew :use-cases:chatbot:build --no-daemon

if %ERRORLEVEL% EQU 0 (
    echo ✓ 修复成功！
    echo 现在可以运行: gradlew :use-cases:chatbot:bootRun
) else (
    echo ✗ 构建仍然失败，请检查其他配置
)

pause
